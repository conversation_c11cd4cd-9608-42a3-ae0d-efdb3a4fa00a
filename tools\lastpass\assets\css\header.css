
.search-container{
  display: flex;
  flex-direction: column; /* 横向布局 */
  align-items: center;
  margin: 0 auto;
  justify-content: center; /* 内容居中 */
  gap: 10px;
  width: 98%;
  max-width: 600px;
}

/* 应用标题 */
.header-title {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-color);
  margin: 1rem auto;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-section {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.15);
  border-radius: 6px;
  border: 1.5px solid rgba(79,140,255,0.18);
  width: 100%;
  transition: box-shadow 0.2s, border 0.2s;
  max-width: 600px;
}

.search-section:focus-within {
  box-shadow: 0 8px 32px 0 rgba(79,140,255,0.25);
  border-color: #4f8cff;
}

.search-section > #searchFilter {
  border: none;
  background: transparent;
  color: #4f8cff;
  font-weight: 500;
  font-size: 15px;
  outline: none;
  box-shadow: 0 1px 2px rgba(79,140,255,0.05);
  cursor: pointer;
  height: 3rem;
  padding: 0 6px;
}

.search-section > .search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #222;
  padding: 3px 10px;
  outline: none;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-left: 1px solid #e0e0e0; /* 左侧浅灰色边框 */

}

/* From Uiverse.io by kamehame-ha */ 
.action-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 15px;
  gap: 15px;
  background-color: #007ACC;
  outline: 3px #007ACC solid;
  outline-offset: -3px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: 400ms;
  height: 3rem;
  width: 100%;
  white-space: nowrap; /* 内容不换行 */

}

.action-btn .text {
  color: white;
  font-weight: 700;
  font-size: 1em;
  transition: 400ms;
}

.action-btn svg path {
  transition: 400ms;
}

.action-btn:hover {
  background-color: transparent;
}

.action-btn:hover .text {
  color: #007ACC;
}

.action-btn:hover svg path {
  fill: #007ACC;
}

.import-export{
  display: flex;
  flex-direction: row; /* 横向布局 */
  gap: 10px;
  width: 100%;
}


@media (min-width:1200px) {
  .search-container{
    flex-direction: row;
    max-width: none;
  }
  .action-btn{
      width:auto;
  }
  .import-export {
    width: auto; /* 让搜索框宽度自适应 */
  }
}

