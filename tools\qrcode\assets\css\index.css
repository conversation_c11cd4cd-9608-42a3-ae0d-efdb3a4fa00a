body {
  margin:0px
}

.col{
  text-align: center;
  margin: 20px 0;
  width: 355px;
}

.row{
  display: grid;
  justify-content: space-evenly;
}
@media screen and (min-width: 360px) {
  .row{
    grid-template-columns: auto;
  }
}

@media screen and (min-width: 720px) {
  .row{
    grid-template-columns: auto auto;
  }
}
@media screen and (min-width: 1080px) {
  .row{
    grid-template-columns: auto auto auto;
  }
}
@media screen and (min-width: 1440px) {
  .row{
    grid-template-columns: auto auto auto auto;
  }
}

.box {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  border-radius: 10px;
  margin: auto;
  padding: 10px 10px 20px 10px;
}

.title{
  font-size: 20px;
  margin-top: 20px;
}
.subtitle{
  font-size: 12px;
  color: #F56C6C;
}
.content{
  font-size: 14px;
  text-indent: 2em;
  color: #909399;
}