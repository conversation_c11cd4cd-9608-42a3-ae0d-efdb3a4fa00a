
body{ 
  margin: 0;padding: 0; background-color: #EEEEEE;
  --color1: #092b00;
  --color2: #ecf5ff;
}

.nav-title{
  border-bottom: 3px #EEEEEE solid;
  line-height: 4.6rem;
  padding: 0;
  margin: 0;
  text-align: center;
}

#app{
  width: 96%;
  margin: 10px auto;
  background-color: white;
  border-radius: 10px;
}

.warp{
  margin: 0 1rem;
}
.sen-content{
  line-height: 3rem;
  border-bottom: 1px #EEEEEE solid;
  text-indent: 2rem;
}

.reply-title{
  background-color: var(--color1);
  /*hue-rotate是颜色滤镜，可以加不同的度数来改变颜色，这里我们使用calc自动计算方法还要var函数来调用我们给每一个a设置不同的属性值1~5然后分别乘以60度就能分别得到不同的度数*/
  filter: hue-rotate(calc(var(--i)*20deg));
  border-radius: 6px;
  line-height: 2rem;
  font-size: 1rem;
  color: white;
  text-indent: 2rem;
  margin: 6px 0;
}

.reply-content{
  font-size: 14px;
  background-color: var(--color2);
  filter: hue-rotate(calc(var(--i)*20deg));
  line-height: 2rem;
  padding: 2px 10px;
  border-radius: 6px;
  margin-right: 6px;
  white-space: nowrap;
}