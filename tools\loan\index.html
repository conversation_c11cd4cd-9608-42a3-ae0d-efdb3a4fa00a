<!DOCTYPE html>
<html lang="en">

<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8" />
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" /><!-- 兼容标签 -->
  <!-- <link rel="shortcut icon" href="assets/image/9.jpeg" type="image/x-icon" /> -->
  <title>房贷计算器</title>
</head>
<body>
<div id="app">
  <!-- 大盒子 -->
  <div class="container">
    <div class="warn"><span>房贷计算器计算结果仅供参考</span></div>
    <div class="warp">
      <div class="nav-wrap">
        <!-- 上部分的小盒子 -->
        <div class="tabs">
          <div id="vr">组合贷款</div>
          <!-- 为每个小li添加一个自定义属性，方便与下面相应的小盒子一一对应 -->
          <div onclick="clickTab(0)" class="tab select-tab" >商业贷款</div>
          <div onclick="clickTab(1)" class="tab" >公积金贷款</div>
          <div onclick="clickTab(2)" class="tab" >组合贷款</div>
        </div>
      </div> 
      <!-- 下部分的小盒子 -->
      <div class="content">
        <div class="input-item field" name="business">
          <input type="text" required id="businessAmount" value="100">
          <span>商业贷款</span>
          <span class="field-right">万元</span>
        </div>
        <div class="input-item field" name="accumulation" >
          <input type="text" required id="accumulationAmount" value="80">
          <span>公积金贷款</span>
          <span class="field-right">万元</span>
        </div>
        <div class="input-item">
          <label class="lable">贷款期限：</label><select id="loanTerm"></select>
        </div>
        <div class="input-item field" name="business"  >
          <input type="text" required id="businessRate" value="4.9">
          <span>商贷利率</span>
          <span class="field-right">%</span>
        </div>
        <div class="input-item field" name="accumulation">
          <input type="text" required  id="accumulationRate" value="3.25">
          <span>公积金利率</span>
          <span class="field-right">%</span>
        </div>
        <div class="input-item">
          <div class="radio-group">
            <label for="bx" onclick="repaymentType('bx')"><input type="radio" name="radio" id="bx" checked /><span>等额本息</span></label>
            <label for="bj" onclick="repaymentType('bj')"><input type="radio" name="radio" id="bj"/><span>等额本金</span>
          </div>
        </div>
        <div class="input-item">
          <div class="submit"><button type="submit" onclick="calc()">计 算</button></div>
        </div>
      </div>
    </div><!-- warp结束 -->
    <div class="warp" style="text-align: left;display:none" id="resultBox">
      <div class="result-title">还款数据摘要</div>
      <div class="result-content" id="sumBj">
        <div name="business" id=""><span>商贷利息</span><span>{{businessInterestSum}}元</span></div>
        <div name="accumulation" loantype1=1><span>公积金利息</span><span>{{accumulationInterestSum}}元</span></div>
        <div name="combination"><span>利息总额</span><span>{{repayInterestSum}}元</span></div>
        <div><span>累计还款总额</span><span>{{repaySum}}元</span></div>
        <div><span >{{repayMonthlyLabel}}</span><span>{{repayMonthly}}元</span></div>
        <div><span>最高月付利息</span><span>{{highestMonthInterest}}元</span></div>
      </div>
      <div class="result-content" id="sumBx"></div>
      <div class="result-title">还款数据明细</div>
      <table>
        <thead>
          <tr>
            <th>期次</th>
            <th>偿还本息</th>
            <th>偿还利息</th>
            <th>偿还本金</th>
            <th>剩余本金</th>
          </tr>
        </thead>
        <tbody id="calcResultBj">
          <tr>
            <td>{{period}}</td>
            <td>{{repayAmountInterest}}</td>
            <td>{{repayInterest}}</td>
            <td>{{repayAmount}}</td>
            <td>{{remain}}</td>
          </tr>
        </tbody>
        <tbody id="calcResultBx"></tbody>
      </table>
    </div>
  </div>
</div>
</body>


<script src='../../../assets/decimal.js'></script>
<script>
  function $(id){
    return document.getElementById(id)
  }
  var loanType = 0
  // 1.获取上面tab栏的父盒子和下部分的3个小盒子
  const tab = document.querySelector('.tabs')
  const vr = document.getElementById('vr')
  // 清除选中
  function clearSelect(){
    for(let i=1,len = tab.children.length;i< len;i++){
      tab.children[i].classList.remove('select-tab')
    }
  }

  function vrStyle(vrWidth,vrSpace){
    vr.style.width = vrWidth + "px"
    vr.style.transform = "translateX("+vrSpace+"px)"
  }

  function clickTab(index){
    loanType = index
    show(index)
    clearSelect()
    tab.children[index+1].classList.add('select-tab')
    vrWidth(index)
    $('resultBox').style.display = 'none'
  }
  
  function show(index){
    var businessDome = document.getElementsByName('business')
    var accumulationDome = document.getElementsByName('accumulation')
    var combinationDome = document.getElementsByName('combination')
    // console.log(businessDome)
    businessDome.forEach(element => {
      if(0==index||2==index) {
        element.style.display = 'block'
      } else {
        element.style.display = 'none'
      }
    });
    accumulationDome.forEach(element => {
      if(1==index||2==index) {
        element.style.display = 'block'
      } else {
        element.style.display = 'none'
      }
    });
    combinationDome.forEach(element => {
      if(2==index) {
        element.style.display = 'block'
      } else {
        element.style.display = 'none'
      }
    });
  }

  function vrWidth(index){
    index = index+1
    let vrSpace = tab.children[index].offsetLeft //返回父元素向左位移宽度
    let vrWidth = tab.children[index].offsetWidth //返回元素的宽度
    vrStyle(vrWidth,vrSpace)
  }
  clickTab(0)
  vrStyle(58,0)

  function val(id){
    let val = $(id).value
    let result = 0
    if(val===undefined||val==null||val==''||val==0){
    } else {
      result = val
    }
    return result
  }

  var loanTermSelect = $('loanTerm')
  for(let i=30;i>0;i--){
    let option = document.createElement('option');
    option.value = i*12; // 设置option的value
    option.text = i+'年('+i*12+'期)'; // 设置option的显示文本
    if(i==20){
      option.selected = true
    }
    loanTermSelect.appendChild(option)
  }
  let optionLast = document.createElement('option');
  optionLast.value = 6; // 设置option的value
  optionLast.text = '半年(6期)'; // 设置option的显示文本
  loanTermSelect.appendChild(optionLast);
  // loanTermSelect

  var calcDome = $('calcResultBj').innerHTML;
  var sumDome = $('sumBj').innerHTML;

  function calc(){
    let businessAmount = Number(new Decimal(val('businessAmount')).times(10000))
    let businessRate = Number(new Decimal(val('businessRate')).dividedBy(100))
    let period = val('loanTerm')
    let accumulationAmount = Number(new Decimal(val('accumulationAmount')).times(10000))
    let accumulationRate = Number(new Decimal(val('accumulationRate')).dividedBy(100))
    // console.log(accumulationAmount)
    
    let businessMonthRate = new Decimal(businessRate).dividedBy(12)
    let accumulationMonthRate = new Decimal(accumulationRate).dividedBy(12)

    // 商业贷款-本金
    let businessBj = calcBj(businessAmount,businessRate,period)
    // 公积金贷款-本金
    let accumulationBj = calcBj(accumulationAmount,accumulationRate,period)
    // 商业贷款-本息
    let businessBx = calcBx(businessAmount,businessRate,period)
    // 公积金贷款-本息
    let accumulationBx = calcBx(accumulationAmount,accumulationRate,period)

    var calcHtmlBj = ""
    var calcHtmlBx = ""

    let sumBjJson = {}
    let sumBxJson = {}
    // 等额本金
    // (4)还款总额=(还款月数+1)*贷款额*月利率/2+贷款额。
    let businessRepayInterestBjSum = new Decimal(new Decimal(period).plus(1)).times(businessAmount).times(businessRate).dividedBy(12).dividedBy(2)  //商业总利息
    let businessRepayBjSum = businessRepayInterestBjSum.plus(businessAmount) // 商业本息总额
    let accumulationRepayInterestBjSum = new Decimal(new Decimal(period).plus(1)).times(accumulationAmount).times(accumulationRate).dividedBy(12).dividedBy(2) //公积金总利息
    let accumulationRepayBjSum = accumulationRepayInterestBjSum.plus(accumulationAmount) // 公积金本息总额
    sumBjJson['repaySum'] = Number(new Decimal(loanType!=0?accumulationRepayBjSum:0).plus(loanType!=1?businessRepayBjSum:0)).toFixed(2)
    sumBjJson['repayInterestSum'] = Number(new Decimal(loanType!=0?accumulationRepayInterestBjSum:0).plus(loanType!=1?businessRepayInterestBjSum:0)).toFixed(2)
    sumBjJson['businessInterestSum'] = Number(businessRepayInterestBjSum).toFixed(2)
    sumBjJson['accumulationInterestSum'] = Number(accumulationRepayInterestBjSum).toFixed(2)
    // console.log(businessRepayInterestBjSum)
    // 等额本息
    // (1)每月还本付息金额=[本金x月利率x(1+月利率)贷款月数]/[(1+月利率)还款月数-1];
    let bxBusiness = new Decimal(businessMonthRate).plus(1).pow(period)
    let bxAccumulation = new Decimal(accumulationMonthRate).plus(1).pow(period)
    // 商业贷款
    // (4)还款总额=还款月数*贷款额*月利率*(1+月利率)贷款月数/(1+月利率)还款月数-1]。
    let businessRepayBxSum = new Decimal(businessAmount).times(businessMonthRate).times(bxBusiness).dividedBy(bxBusiness.minus(1)).times(period)
    // (3)还款总利息=贷款额*贷款月数*月利率*(1+月利率)贷款月数/(1+月利率)还款月数-1]-贷款额:
    let businessRepayInterestBxSum = businessRepayBxSum.minus(businessAmount) // 偿还利息总额
    // 公积金
    // (4)还款总额=还款月数*贷款额*月利率*(1+月利率)贷款月数/(1+月利率)还款月数-1]。
    let accumulationRepayBxSum = new Decimal(accumulationAmount).times(accumulationMonthRate).times(bxAccumulation).dividedBy(bxAccumulation.minus(1)).times(period)
    // (3)还款总利息=贷款额*贷款月数*月利率*(1+月利率)贷款月数/(1+月利率)还款月数-1]-贷款额:
    let accumulationRepayInterestBxSum = accumulationRepayBxSum.minus(accumulationAmount) // 偿还利息总额

    sumBxJson['repaySum'] = Number(new Decimal(loanType!=0?accumulationRepayBxSum:0).plus(loanType!=1?businessRepayBxSum:0)).toFixed(2)
    sumBxJson['repayInterestSum'] = Number(new Decimal(loanType!=0?accumulationRepayInterestBxSum:0).plus(loanType!=1?businessRepayInterestBxSum:0)).toFixed(2)
    sumBxJson['businessInterestSum'] = Number(businessRepayInterestBxSum).toFixed(2)
    sumBxJson['accumulationInterestSum'] = Number(accumulationRepayInterestBxSum).toFixed(2)

    for(let i=0;i<period;i++){
      if(i==0){
        sumBjJson['repayMonthly'] = Number(new Decimal(loanType!=0?accumulationBj[i].repayAmountInterest:0).plus(loanType!=1?businessBj[i].repayAmountInterest:0))
        sumBjJson['highestMonthInterest'] = Number(new Decimal(loanType!=0?accumulationBj[i].repayInterest:0).plus(loanType!=1?businessBj[i].repayInterest:0))

        sumBxJson['repayMonthly'] = Number(new Decimal(loanType!=0?accumulationBx[i].repayAmountInterest:0).plus(loanType!=1?businessBx[i].repayAmountInterest:0))
        sumBxJson['highestMonthInterest'] = Number(new Decimal(loanType!=0?accumulationBx[i].repayInterest:0).plus(loanType!=1?businessBx[i].repayInterest:0))
      }
      calcHtmlBj += calcDome
        .replace('{{period}}', (i+1)+'/'+period+'期')
        .replace('{{repayAmountInterest}}', Number(new Decimal(loanType!=0?accumulationBj[i].repayAmountInterest:0).plus(loanType!=1?businessBj[i].repayAmountInterest:0)))
        .replace('{{repayInterest}}', Number(new Decimal(loanType!=0?accumulationBj[i].repayInterest:0).plus(loanType!=1?businessBj[i].repayInterest:0)))
        .replace('{{repayAmount}}', Number(new Decimal(loanType!=0?accumulationBj[i].repayAmount:0).plus(loanType!=1?businessBj[i].repayAmount:0)))
        .replace('{{remain}}', Number(new Decimal(loanType!=0?accumulationBj[i].remain:0).plus(loanType!=1?businessBj[i].remain:0)))

      calcHtmlBx += calcDome
        .replace('{{period}}', (i+1)+'/'+period+'期')
        .replace('{{repayAmountInterest}}', Number(new Decimal(loanType!=0?accumulationBx[i].repayAmountInterest:0).plus(loanType!=1?businessBx[i].repayAmountInterest:0)))
        .replace('{{repayInterest}}', Number(new Decimal(loanType!=0?accumulationBx[i].repayInterest:0).plus(loanType!=1?businessBx[i].repayInterest:0)))
        .replace('{{repayAmount}}', Number(new Decimal(loanType!=0?accumulationBx[i].repayAmount:0).plus(loanType!=1?businessBx[i].repayAmount:0)))
        .replace('{{remain}}', Number(new Decimal(loanType!=0?accumulationBx[i].remain:0).plus(loanType!=1?businessBx[i].remain:0)))
    }
    $('calcResultBj').innerHTML = calcHtmlBj
    $('calcResultBx').innerHTML = calcHtmlBx
    $('sumBj').innerHTML = sumDome
        .replace('{{repaySum}}',sumBjJson.repaySum)
        .replace('{{repayInterestSum}}',sumBjJson.repayInterestSum)
        .replace('{{businessInterestSum}}',sumBjJson.businessInterestSum)
        .replace('{{repayMonthly}}',sumBjJson.repayMonthly)
        .replace('{{highestMonthInterest}}',sumBjJson.highestMonthInterest)
        .replace('{{accumulationInterestSum}}',sumBjJson.accumulationInterestSum)
        .replace('{{repayMonthlyLabel}}','最高月付')
    $('sumBx').innerHTML = sumDome
        .replace('{{repaySum}}',sumBxJson.repaySum)
        .replace('{{repayInterestSum}}',sumBxJson.repayInterestSum)
        .replace('{{businessInterestSum}}',sumBxJson.businessInterestSum)
        .replace('{{repayMonthly}}',sumBxJson.repayMonthly)
        .replace('{{highestMonthInterest}}',sumBxJson.highestMonthInterest)
        .replace('{{accumulationInterestSum}}',sumBxJson.accumulationInterestSum)
        .replace('{{repayMonthlyLabel}}','每月月供')
    show(loanType)
    $('resultBox').style.display = 'block'
  }

  function calcBj(amount,rate,period){
    let monthRate = new Decimal(rate).dividedBy(12)
    // (2)每月本金=总本金/还款月数;
    let repayAmount = new Decimal(amount).dividedBy(period)
    let remain = amount // 剩余本金

    let list = []// 返回json值
    for(let i=1;i<=period;i++){
      let result = {}
      // 本月偿还本金
      result['repayAmount'] = Number(repayAmount).toFixed(2)
      // 本月偿还利息 - (3)每月利息=(本金-累计已还本金)x月利率;
      result['repayInterest'] = Number(new Decimal(remain).times(monthRate)).toFixed(2)
      // 本月偿还本息 - 还款本息 = 每月本金 + 每月利息 (1)每月还本付息金额=(本金/还款月数)+(本金-累计已还本金)x月利率;
      result['repayAmountInterest'] = Number(repayAmount.plus(result.repayInterest)).toFixed(2)
      // 本月剩余本金 = 上月剩余本金 - 本月归还本金
      remain = Number(new Decimal(remain).minus(repayAmount)).toFixed(2)
      result['remain'] = remain
      list.push(result)
    }
    return list
  }

  function calcBx(amount,rate,period){
    let monthRate = new Decimal(rate).dividedBy(12)
    // (1)每月还本付息金额=[本金x月利率x(1+月利率)贷款月数]/[(1+月利率)还款月数-1];
    let bxA = new Decimal(monthRate).plus(1).pow(period)
    let repayAmountInterest = new Decimal(amount).times(monthRate).times(bxA).dividedBy(bxA.minus(1))
    let remain = amount        // 剩余本金
 
    let list = []// 返回json值
    for(let i=1;i<=period;i++){
      let result = {}
      // 本月偿还利息 - (2)每月利息=剩余本金x贷款月利率;
      result['repayInterest'] = Number(new Decimal(remain).times(monthRate)).toFixed(2)
      // 本月偿还本息 - 还款本息 = 每月本金 + 每月利息 (1)每月还本付息金额=(本金/还款月数)+(本金-累计已还本金)x月利率;
      result['repayAmountInterest'] = Number(repayAmountInterest).toFixed(2)
      // 本月偿还本金
      result['repayAmount'] = Number(repayAmountInterest.minus(result.repayInterest)).toFixed(2)
      // 本月剩余本金 = 上月剩余本金 - 本月归还本金
      remain = Number(new Decimal(remain).minus(result.repayAmount)).toFixed(2)
      result['remain'] = remain
      list.push(result)
    }

    return list
  }

  // decimal.js用法
  // 加：plus
  // 减：minus
  // 乘：times
  // 除：dividedBy
  // 除余：modulo
  // 幂：pow

  function repaymentType(type){
    if(type=='bx'){
      // console.log(type)
      $('calcResultBj').style.display = 'none'
      $('calcResultBx').style.display = 'table-row-group'
      $('sumBj').style.display = 'none'
      $('sumBx').style.display = 'block'
    }
    if(type=='bj'){
      // console.log(type)
      $('calcResultBx').style.display = 'none'
      $('calcResultBj').style.display = 'table-row-group'
      $('sumBx').style.display = 'none'
      $('sumBj').style.display = 'block'
    }
  }

  function changeNum(obj) {
    //如果用户第一位输入的是小数点，则重置输入框内容
    if (obj.value != '' && obj.value.substr(0, 1) == '.') {
      obj.value = "";
    }
    obj.value = obj.value.replace(/^0*(0\.|[1-9])/, '$1');//粘贴不生效
    obj.value = obj.value.replace(/[^\d.,]/g, "");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数
    if (obj.value.indexOf(".") < 0 && obj.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      if (obj.value.substr(0, 1) == '0' && obj.value.length == 2) {
        obj.value = obj.value.substr(1, obj.value.length);
      }
    }
  }

  var inputList = document.getElementsByTagName('input')
  for (var i = 0; i < inputList.length; i++) {
    inputList[i].addEventListener("keydown", function(event) {
      // 获取按下的键值
      var keyCode = event.which || event.keyCode;
      // 利用正则表达式判断是否为数字或者删除、后退等特定操作
      if (!((keyCode >= 48 && keyCode <= 57) || (keyCode === 8 || keyCode === 9 || keyCode === 37 || keyCode === 39 || keyCode === 110 || keyCode === 190))){
          // 若不符合条件，则阻止默认事件（即输入）发生
          event.preventDefault();
      }
    });
    inputList[i].onkeyup = function () {
      changeNum(this);
    }

  }

  show(0) //初始化贷款状态
  repaymentType('bx')

</script>
<style>
:root{
  --bodybgcolor: #f0f0f0;
	--bgcolor:#ffffff;
  --vrcolor: #409eff;
	--danger:#f5222d;
	--success:#52c41a;
	--lighttext:#8c8c8c;
	--normaltext:#434343;
	--blacktext:#1d2b3a;
}

body {
  background: var(--bodybgcolor);
  margin: 0;
  font-family: Lato;
}

.container {
  display: flex;
  place-content: center;
  justify-content: center;
  flex-direction: column;
}

.warp{
  min-width: 300px;
  max-width: 520px;
  width: 96%;
  margin: 0 auto;
  background: var(--bgcolor);
  border-radius: 6px;
  padding-bottom: 1rem;
}

.nav-wrap{
  overflow: hidden;
}

.nav-wrap > .tabs {
  width: 80%;
  display: flex;
  justify-content: space-between;
  white-space: nowrap;
  position: relative;
  transition: transform .3s;
  z-index: 1;
  margin: 0 auto;
}
.nav-wrap > .tabs::after{
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: #e4e7ed;
  z-index: 1;
}
.nav-wrap .tabs:nth-child(2){
  padding-left: 0 !important;
}
.nav-wrap .tabs:last-child{
  padding-right: 0 !important;
}
.nav-wrap .tabs > .tab {
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  list-style: none;
  font-size: 0.9rem;
  font-weight: 500;
  color: #303133;
  position: relative;
  cursor: pointer;
}
.nav-wrap .tabs > .tab:hover{
  color: var(--vrcolor);
}
#vr{
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background-color: var(--vrcolor);
  z-index: 3;
  transition: width .3s cubic-bezier(.645, .045, .355, 1),transform .3s cubic-bezier(.645, .045, .355, 1);
  list-style: none;
}

.select-tab{
  color: var(--vrcolor) !important;
}

.content {
  overflow: hidden;
  position: relative;
  padding: 1rem;
}

/* 禁止选择文字 */
div {
  -moz-user-select: none;
  /*火狐*/
  -webkit-user-select: none;
  /*webkit浏览器*/
  -ms-user-select: none;
  /*IE10*/
  -khtml-user-select: none;
  /*早期浏览器*/
  user-select: none;
}

.input-item{
  position:relative;
	margin: 1rem auto 0 auto;
  width: 90%;
  display: block;
}

.input-item > .label{
  margin: 0.5rem 0;
}

.field input {
  font-size: inherit;
  width: 100%;
  color: var(--normaltext);
  outline: none;
  display: block;
  font-weight: 400;
  line-height: 2.2rem;
  border-radius: 8px;
  border: 1px solid var(--lighttext);
  background-color: transparent;
  transition: border-color 0.2s ease-in-out;
  background-repeat: no-repeat;
  background-position: right 2% center;
  background-size: 1.2rem  1.2rem;
  text-indent: 0.5rem;
}

input[type="text" i]{
  padding-block: 0px;
  padding-inline: 0px;
}

.field-right{
  position: absolute;
  top: 3px;
  right: 1.2rem;
	padding:0.25rem 0.5rem;
  color: var(--lighttext);
}
.field input + span{
	color:var(--lighttext);
	background-color:var(--bgcolor);
	font-weight:400;
	padding:0.25rem 0.5rem;
	display:inline-block;
	position:absolute;
	top:3px;
	left:10px;
	transition:0.2s all ease-in-out;
	user-select:none;
	pointer-events:none;
  white-space: nowrap;
}
.field input:focus{
	border:1px solid var(--danger);
	background-image:url('data:image/svg+xml;base64,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')
}
.field input:valid{
	border:1px solid var(--success);
	background-image:url('data:image/svg+xml;base64,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')
}
/* 验证失败 */
.field input:focus + span,
.field input:valid + span{
	font-size:0.8rem;
	line-height:1em;
	top:-10px;
}

.field input:focus + span{
	color:var(--danger);
	border-left: 1px solid var(--danger);
	border-right: 1px solid var(--danger);
}

/* 验证成功 */
.field input:valid + span{
	color:var(--success);
	border-left: 1px solid var(--success);
	border-right: 1px solid var(--success);
}

.radio-group{
  display: flex;
  width: 96%;
  margin: 0 auto;
  white-space: nowrap;
  justify-content:space-around;
}
.radio-group label {
	display: flex;
	/* 鼠标移入变小手 */
	cursor: pointer;
}
.radio-group label input {
	/* 隐藏元素 先显示出来 */
	display: none;
}

/* + 是相邻兄弟选择器 */
.radio-group > label input + span {
	display: flex;
	/* 让伪元素和文字水平排列 */
	align-items: center;
	padding: 6px 12px;
  font-size: 1rem;
	transition: all 0.3s;
	border-radius: 11rem;
  margin-bottom: 0.5;
  /* border: #edd8e0 1px solid; */
}
/* 鼠标移入变背景颜色 */
.radio-group > label input + span:hover {
	background-color: #edd8e0;
}
/* 选中单选按钮对应的span元素背景颜色改变 */
.radio-group > label input:checked + span {
	background-color: #edd8e0;
}
.radio-group > label input + span::before {
	content: "";
	/* 伪元素是行内元素 需要转为块级元素才能设置宽高 */
	display: block;
	width: 1.6rem;
	height: 1.6rem;
	/* 圆角属性 */
	border-radius: 50%;
	margin-right: 0.5rem;
	background-color: #fff;
	/* 盒子阴影 inset是内部阴影 */
	box-shadow: 0 0 0 5px #900c3f inset;
	/* 加个过渡时间 */
	transition: all 0.3s;
}
.radio-group > label input:checked + span::before {
	/* 选中的元素内部的阴影加深 */
	box-shadow: 0 0 0 15px #900c3f inset;
}


.warn{
  text-align: center;
  color: #f5222d;
  font-size: 0.8rem;
  line-height: 3rem;
}
.input-item > .label{
  font-size: 0.9rem;
  width: 80px;
  white-space: nowrap;
}

#loanTerm {
  width: calc(100% - 80px);
  font-weight: 400;
  height: 2rem;
  padding: 6px;
  border-radius: 8px;
  border: 1px solid var(--lighttext);
  background-size: 1.2rem 1.2rem;
}

.submit > button{
  width: 100%;
  height: 3rem;
  border-radius: 8px;
  font-size: large;
  cursor: pointer;
  border: 2px solid rgb(79, 54, 74);
  background-color: rgb(181, 150, 181);
  font-weight: bold;
}

#resultBox >table{
  width: 100%;
  text-align: center;
}

.result-title{
  line-height: 2rem;
  text-indent: 1rem;
  background-color: rgb(181, 150, 181);
}
.result-content{
  padding: 0 1rem;
}
.result-content>div{
  line-height: 2rem;
  font-size: 0.9rem;
  border-bottom: 1px solid rgb(79, 54, 74);
}

.result-content>div>span:first-child{
  font-weight: bold;
}
.result-content>div>span:nth-last-child(1){
  float: right;
}
</style>

</html>