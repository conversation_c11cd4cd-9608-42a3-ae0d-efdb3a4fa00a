
<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="stylesheet" type="text/css" href="assets/index.css">
  <link rel="shortcut icon" href="assets/image/ipad.jpeg" type="image/x-icon"/>
  <title>我的背包</title>
</head>
<body>
<div id="app" class="container">
  <div class="wrap" id="wrap">
    <div class="box" id="box">
      <div class="overlay-container" >
        <img src="{{icon}}" alt="{{title}}" width="200px" height="200px" class="coverd">
        <div class="overlap">
          <p class="card-title">{{title}}</p>
          <p class="card-price">{{price}}</p>
          <p class="card-lifetime">{{lifetime}}</p>
          <p class="card-remark">{{remark}}</p>
        </div>
      </div>
    </div>
  </div>
</div>
</body>
<script>
const knapsackDatas = [
  { typeID: "1",icon:"assets/image/ipad.jpeg",title: "iPad Pro 2020",price:"￥7899（教育优惠，送iPod 2）",lifetime:"2019年10月-至今", remark: "买前生产力，买后盖泡面" },
  { typeID: "1",icon:"assets/image/dn.jpeg",title: "东芝L50-A",price:"￥4199",lifetime:"2014年生-2020年卒（拆坏的）", remark: "第一台电脑，性能强大，质量强大，东芝永远的神，但被小孩暴打了一顿，从此一蹶不振" },
  { typeID: "1",icon:"assets/image/tsdn.jpeg",title: "R5 5600X组装机",price:"约￥10000",lifetime:"2015年9月-至今", remark: "组装机，前前后后换过几次配件，显卡”七彩虹960 4G“一直没换过" },
  { typeID: "2",icon:"assets/image/tzc.jpeg",title: "华为智选体脂秤",price:"￥41（京东双十一优惠券）",lifetime:"2021年11月-至今", remark: "减肥的意志" },
  { typeID: "2",icon:"assets/image/ddys.jpeg",title: "华为智选电动牙刷",price:"￥44（京东双十一优惠券）",lifetime:"2021年11月-至今", remark: "刷了一次，牙疼一星期" },
  { typeID: "2",icon:"assets/image/sxt.jpeg",title: "华为智选摄像头Pro",price:"￥188（京东双十一优惠券）",lifetime:"2021年11月-至今", remark: "除了浪费电没其他用处" },
  { typeID: "2",icon:"assets/image/sj2.jpeg",title: "华为nova 9",price:"￥2699",lifetime:"2021年11月-至今", remark: "就是想体验一下华为系统，没想到剁了只手" },
  { typeID: "3",icon:"assets/image/sj1.jpeg",title: "OnePlus 7T",price:"￥3199",lifetime:"2019年10月-至今", remark: "iPone13出来前”这破手机好像不行了，用电太快了，打开软件有时候还卡“，iPone13出来后，好像还能再撑一年" },
  { typeID: "4",icon:"assets/image/yddy.jpeg",title: "罗马仕移动电源",price:"￥99",lifetime:"2019年12月-至今", remark: "没用过几次，压根就不远行的" },
  { typeID: "4",icon:"assets/image/xyj.jpeg",title: "西门子洗衣机",price:"￥2999",lifetime:"2022年1月-至今", remark: "用了好久的便宜洗衣机终于用上的好的洗衣机了" },
]
const knapsackTypes = [
  { id: "1",icon:"",name: "生产力" },
  { id: "2",icon:"",name: "智慧生活" },
  { id: "3",icon:"",name: "附带" },
  { id: "4",icon:"",name: "其他" },
]

// 通用函数
function $(selector) {
  var method = selector.substr(0, 1) == '.' ? 'getElementsByClassName' : 'getElementById';
  return document[method](selector.substr(1));
}

function init() {
  var template = $('#box').innerHTML;
  var html = [];
  knapsackTypes.forEach(types => {
    html.push('<div class="head-title">' + types.name + '</div><div class="box">');
    knapsackDatas.forEach(datas => {
      if(datas.typeID === types.id){
        let _html = template
          .replace('{{title}}', datas.title)
          .replace('{{title}}', datas.title)
          .replace('{{price}}', datas.price)
          .replace('{{lifetime}}', datas.lifetime)
          .replace('{{remark}}', datas.remark)
          .replace('{{icon}}', datas.icon)
        html.push(_html);
      }
    });
    html.push('</div>')
  })
  console.log(html)
  $('#wrap').innerHTML = html.join('');
}

init()

</script>
</html>

