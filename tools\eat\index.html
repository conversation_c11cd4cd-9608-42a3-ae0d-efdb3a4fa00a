<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <link rel="shortcut icon" href="../../door/assets/icon/logo.png" type="image/x-icon"/>
  <link rel="stylesheet" type="text/css" href="assets/css/index.css"/>
  <title>今天吃啥呀?</title>
</head>
<body style="background-image: url('assets/images/bg.jpg');">
<div id="app" style="overflow: hidden;">
  <div id="show"></div>
  <div class="title">
    <img src="assets/images/logo.png" alt="今天吃啥呀">
    <p id="cuisineM"></p>
  </div>
  <div class="content">
    <h1 class="eat" id="eatTextShow"></h1>
    <input 
      class="start" 
      type="button" 
      id="startBtn"
      value="开始"
    />
    <h1 class="eat" id="fallbackText" style="display:none;">这么挑，喝西北风吧</h1>
    <p class="more">
      <span id="restartBtn" style="display:none;"><i class="iconfont icon-shuaxin" style="font-size: 13px;"></i> 重新开始</span>
      <span id="customBtn" class="show">自定义菜单</span>
      <span id="chooseBtn" class="show">选择菜单 <i class="iconfont icon-caidan" style="font-size: 13px;"></i></span>
    </p>
    <div class="note">
      <p id="noteShow" class="hide"></p>
    </div>
  </div>
  <div id="popupWrap" style="display:none;">
    <div class="popup"></div>
    <div class="popup-box">
      <h3>自定义临时菜单<small>- 菜单名要以空格区分 -</small></h3>
      <textarea id="eatText"></textarea>
      <button class="btn-ok" type="button" id="okBtn">确定</button>
    </div>
  </div>
  <div id="cuisineWrap" style="display:none;">
    <div class="popup"></div>
    <div class="popup-box">
      <h3 class="cuisine-title">选择菜单</h3>
      <div style="text-align: left;" id="cuisineMenuBox"></div>
    </div>
  </div>
  <div class="fonter">
    <a href="http://maran.fun" class="footer-a">我的首页</a>
    <span class="fonter-font"><a href="http://maran.fun" class="footer-a">关于</a></span>
  </div>
</div>
<script type="text/javascript" src="assets/data.js"></script>
<script>
(function(){
  // 数据初始化
  let eat = "";
  let startFlag = true;
  let countClick = 0;
  let eatTmp = cuisine[0];
  let eatData = splitArr(cuisine[0]);
  const fontColor = ["#67C23A","#E6A23C","#F56C6C","#909399","#409EFF","#fa8c16","#f5222d","#faad14","#fadb14","#a0d911","#52c41a","#13c2c2","#13c2c2","#1890ff","#2f54eb","#722ed1","#eb2f96","#1890ff"];
  let popupFlag = false;
  let cuisineFlag = false;
  const clickText = ["开始","不行，换一个","不行，再换一个","不行，还要换一个","不行，最后再换一次"];
  let cuisineM = "家常菜";
  let noteData = splitArr(note);
  let noteShow = "";

  // DOM
  const showDiv = document.getElementById("show");
  const cuisineMEl = document.getElementById("cuisineM");
  const eatTextShow = document.getElementById("eatTextShow");
  const startBtn = document.getElementById("startBtn");
  const fallbackText = document.getElementById("fallbackText");
  const restartBtn = document.getElementById("restartBtn");
  const customBtn = document.getElementById("customBtn");
  const chooseBtn = document.getElementById("chooseBtn");
  const noteShowEl = document.getElementById("noteShow");
  const popupWrap = document.getElementById("popupWrap");
  const okBtn = document.getElementById("okBtn");
  const eatTextArea = document.getElementById("eatText");
  const cuisineWrap = document.getElementById("cuisineWrap");
  const cuisineMenuBox = document.getElementById("cuisineMenuBox");

  // 初始化菜单名
  cuisineMEl.textContent = cuisineM;
  updateEatText();

  // 事件绑定
  startBtn.onclick = function() {
    if (countClick >= clickText.length) return;
    if (startFlag) {
      startEat();
    } else {
      stopEat();
    }
  };

  restartBtn.onclick = function() {
    countClick = 0;
    eat = "";
    noteShow = "";
    updateEatText();
    updateNote();
    fallbackText.style.display = "none";
    eatTextShow.style.display = "";
    restartBtn.style.display = "none";
    startBtn.value = clickText[countClick];
    startBtn.style.display = "";
    customBtn.className = "show";
    chooseBtn.className = "show";
  };

  customBtn.onclick = function() {
    popupFlag = true;
    popupWrap.style.display = "";
    eatTextArea.value = eatTmp;
  };
  popupWrap.querySelector('.popup').onclick = function() {
    popupFlag = false;
    popupWrap.style.display = "none";
  };
  okBtn.onclick = function() {
    var eatEdit = eatTextArea.value;
    var arr = splitArr(eatEdit);
    if(arr.length<2){
      switch(arr.length){
        case 1:
          alert("爬！！！一个菜也要纠结？？？？");
          break;
        case 0:
          alert("没菜你还吃啥呢，吃西北风去吧");
          break;
      }
      return;
    }
    eatTmp = eatEdit;
    cuisineM = "自定义临时菜单";
    cuisineMEl.textContent = cuisineM;
    eatData = arr;
    popupFlag = false;
    popupWrap.style.display = "none";
    countClick = 0;
    eat = "";
    updateEatText();
    updateNote();
    fallbackText.style.display = "none";
    eatTextShow.style.display = "";
    restartBtn.style.display = "none";
    startBtn.value = clickText[countClick];
    startBtn.style.display = "";
    customBtn.className = "show";
    chooseBtn.className = "show";
  };

  chooseBtn.onclick = function() {
    cuisineFlag = true;
    cuisineWrap.style.display = "";
    renderCuisineMenu();
  };
  cuisineWrap.querySelector('.popup').onclick = function() {
    cuisineFlag = false;
    cuisineWrap.style.display = "none";
  };

  function renderCuisineMenu() {
    cuisineMenuBox.innerHTML = cuisineMenu.map((menu, idx) =>
      `<span class="cuisine-menu" data-idx="${idx}">${menu}</span>`
    ).join('');
    Array.from(cuisineMenuBox.querySelectorAll('.cuisine-menu')).forEach(span => {
      span.onclick = function() {
        let id = +this.getAttribute('data-idx');
        eatTmp = cuisine[id];
        cuisineM = cuisineMenu[id];
        cuisineMEl.textContent = cuisineM;
        eatData = splitArr(cuisine[id]);
        cuisineFlag = false;
        cuisineWrap.style.display = "none";
        countClick = 0;
        eat = "";
        updateEatText();
        updateNote();
        fallbackText.style.display = "none";
        eatTextShow.style.display = "";
        restartBtn.style.display = "none";
        startBtn.value = clickText[countClick];
        startBtn.style.display = "";
        customBtn.className = "show";
        chooseBtn.className = "show";
      };
    });
  }

  function startEat() {
    let len = eatData.length;
    interval1 = setInterval(() => {
      var domSpan = document.createElement("span");
      domSpan.setAttribute('style','left: ' + (Math.floor(Math.random() * 90)) 
                        + '%;top: ' + (Math.floor(Math.random() * 90))
                        + '%;color:'+ fontColor[Math.floor(Math.random() * fontColor.length)] 
                        + ';font-size:' + (Math.floor(Math.random() * 36))
                        + 'px;opacity:' + (Math.random()));
      var rnd = Math.floor(Math.random() * len)
      domSpan.innerText = eatData[rnd]
      domSpan.className = "child-text"
      showDiv.appendChild(domSpan);
      eat = eatData[rnd];
      updateEatText();
      if(showDiv.childNodes.length > 30){
        showDiv.removeChild(showDiv.childNodes[0])
      }
    },30);
    startBtn.value = "停止";
    startFlag = false;
    customBtn.className = "hide";
    chooseBtn.className = "hide";
  }

  function stopEat() {
    // 清除动画
    while(showDiv.firstChild) showDiv.removeChild(showDiv.firstChild);
    clearInterval(interval1);
    noteShow = noteData[Math.floor(Math.random() * noteData.length)];
    updateNote();
    countClick = countClick < clickText.length ? countClick+1 : 0;
    if(countClick >= clickText.length){
      fallbackText.style.display = "";
      eatTextShow.style.display = "none";
      restartBtn.style.display = "";
      startBtn.style.display = "none";
      customBtn.className = "hide";
      chooseBtn.className = "hide";
    } else {
      fallbackText.style.display = "none";
      eatTextShow.style.display = "";
      restartBtn.style.display = "none";
      startBtn.value = clickText[countClick];
      startBtn.style.display = "";
      customBtn.className = "show";
      chooseBtn.className = "show";
    }
    startFlag = true;
  }

  function updateEatText() {
    eatTextShow.textContent = countClick < clickText.length ? eat : "";
  }
  function updateNote() {
    noteShowEl.textContent = noteShow;
    noteShowEl.className = (startFlag && countClick < clickText.length && countClick != 0) ? "show" : "hide";
  }

  function splitArr(arr){
    eat = "";
    countClick = 0;
    return arr.trim().split(" ").filter(Boolean);
  }

  // 初始化
  updateEatText();
  updateNote();
  startBtn.value = clickText[countClick];
})();
</script>
</body>
</html>