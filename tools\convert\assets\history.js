let historyList = [];
const HISTORY_KEY = 'convert_history_v1';

function loadHistory() {
  try {
    const data = localStorage.getItem(HISTORY_KEY);
    if (data) {
      historyList = JSON.parse(data);
    }
  } catch {}
}
function saveHistory() {
  try {
    localStorage.setItem(HISTORY_KEY, JSON.stringify(historyList.slice(0, 10)));
  } catch {}
}
function addHistory(input) {
  if (!input.trim()) return;
  if (historyList[0] !== input) {
    historyList.unshift(input);
    if (historyList.length > 10) historyList.length = 10;
    saveHistory();
    renderHistory();
  }
}
function renderHistory() {
  let hisBox = document.getElementById('historyBox');
  if (!hisBox) {
    hisBox = document.createElement('div');
    hisBox.id = 'historyBox';
    hisBox.className = 'row';
    document.getElementById('app').insertBefore(hisBox, document.getElementById('app').children[1]);
  }
  if (!historyList.length) {
    hisBox.innerHTML = '';
    return;
  }
  hisBox.innerHTML = `<div style="margin:10px 0 6px 0;text-align:left;color:#666;font-size:13px;">历史记录：</div>` +
    historyList.map((item, idx) =>
      `<div class="history-item" onclick="fillHistory(${idx})" title="点击填入">${item.replace(/</g,"&lt;").replace(/>/g,"&gt;").slice(0,40)}${item.length>40?'...':''}</div>`
    ).join('');
}
window.fillHistory = function(idx) {
  document.getElementById('inputText').value = historyList[idx];
  document.getElementById('inputText').focus();
  document.getElementById('convertBtn').disabled = !historyList[idx].trim();
};