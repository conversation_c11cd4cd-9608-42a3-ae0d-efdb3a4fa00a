var data = [
  {
    "createTime": "2021-12-04",
    "title": "极简主义",
    "version":"v1.1",
    "content": "主要模块有：首页、记事本、常用链接、常用二维码、时间线，遵循极简主义，太麻烦的东西反而不好整理",
    "picture": "",
    "color": ""
  },{
    "createTime": "2021-12-11",
    "title": "功能报告",
    "version":"v1.2",
    "content": "目前已有12个想法，其中9个基本实现，3个未实现",
    "picture": "",
    "color": ""
  },{
    "createTime": "2021-12-03",
    "title": "初心",
    "version":"v1.0",
    "content": "1.坚持使用全本地无联网、无外部依赖<br>2.使用框架有：vue2、bootstrap5、iconfont<br>3.开发24个小玩具，以学习为主，实用为辅，尽大可能使用原生代码",
    "picture": "",
    "color": ""
  },{
    "createTime": "2022-01-26",
    "title": "年前更新",
    "version":"v1.3",
    "content": "1.新增打字机模块<br>2.修改标题为各个页面的自定义标题<br>3.更新我的背包内容",
    "picture": "",
    "color": ""
  },{
    "createTime": "2022-03-17",
    "title": "今天吃什么",
    "version":"v1.4",
    "content": "1.新增今天吃啥呀？模块<br>2.优化了以前的数据页面",
    "picture": "",
    "color": ""
  },{
    "createTime": "2023-12-20",
    "title": "重新设计页面架构",
    "version":"v1.5",
    "content": "1.新增计时器页面<br>2.重新构建了页面",
    "picture": "",
    "color": ""
  },{
    "createTime": "2024-03-12",
    "title": "房贷计算器整了我两天半",
    "version":"v1.6",
    "content": "1.新增计算个人所得税页面<br>2.新增房贷计算器页面",
    "picture": "",
    "color": ""
  },{
    "createTime": "2024-03-15",
    "title": "更新了一波图片",
    "version":"v1.7",
    "content": "1.更改主页和流体图片展示的图片，还是要显得正常点",
    "picture": "",
    "color": ""
  }
]