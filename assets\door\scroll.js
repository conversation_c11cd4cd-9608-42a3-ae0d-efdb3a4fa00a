

/* 测导航栏固定 */
var menuSidebar = className("menu-sidebar-warp")
var scrollFunc = function () {
  let sideTop = className("nav-container").scrollHeight + className('search-container').scrollHeight;
  let st = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
  if(st > sideTop){ 
    menuSidebar.classList.add("fixed")
    className("fixed-bar-up").classList.add("show")
  } else { 
    menuSidebar.classList.remove("fixed")
    className("fixed-bar-up").classList.remove("show")
  }
}
window.onscroll = document.onscroll = scrollFunc;


var sideIndex = 0;
function sideScroll(e,end) {
    let sidebarScroll = document.getElementsByClassName("menu-sidebar-item-a");
    if(sideIndex < end){
        for(let i = sideIndex,len=end; i<=len;i++) {
        autoSide(sidebarScroll[i],i,sideIndex)
        }
    }else{
        for(let i = sideIndex,len=end; i>=len;i--) {
        autoSide(sidebarScroll[i],i,sideIndex)
        }
    }
    e.preventDefault();
    const targetId = e.target.getAttribute('href');
    const targetElement = document.querySelector(targetId);
    
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
    sideIndex = end;
}

function autoSide(element,i,start){
  setTimeout(function(){
    removeClassItem('menu-sidebar-item-a','active')
    let goElement = className("menu-sidebar-go").querySelector('span');
    let space = 13+31.5*i
    goElement.style.cssText = "top:"+space+"px;";
    element.classList.add("active")
  },Math.abs(start-i)*100)
}



 