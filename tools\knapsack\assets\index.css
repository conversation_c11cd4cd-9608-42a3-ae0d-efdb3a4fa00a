/* 禁止选择文字 */
div{
  -moz-user-select:none;/*火狐*/
  -webkit-user-select:none;/*webkit浏览器*/
  -ms-user-select:none;/*IE10*/
  -khtml-user-select:none;/*早期浏览器*/
  user-select:none;
}

.container{
  width: 96%;
  margin: 30px auto;
}

.head-title{
  width: 100%;
  font-size: 2rem;
  font-weight: bold;
  line-height: 3rem;
  margin: 10px 0;
  white-space: nowrap;
}

.box{
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-align-content: center;
  align-content: center;
  transition: 0.5s;
}

.overlay-container:hover > .overlap > .card-title{
  color: #efefef;
  font-size: 16px;
}
.overlay-container:hover > .overlap > .card-price{
  color: #ffa39e;
}
.overlay-container:hover > .overlap > .card-lifetime{
  color: #fff1b8;
}
.overlay-container:hover > .overlap > .card-remark{
  color: #d9d9d9;
}

/* 图片放大、蒙版文字 */
.overlay-container {
  position: relative;
  min-width: 50px;
  min-height: 50px;
  overflow: hidden;
  margin: 3px;
  border-radius: 10px;
  cursor: pointer;
}
.overlay-container > .coverd {
  transition:all .3s;
}
.overlay-container > .overlap {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  padding-top: 0;
  font-size: 12px;
  text-align: center;
  color: transparent;
  transition:all .3s;
}
.overlay-container > .overlap > p {
  color: transparent;
  font-size: 12px;
  margin-bottom: 10px;
  padding: 0px 5px;
}
.overlay-container:hover > .coverd {
  transform: scale(1.3, 1.3);
}
.overlay-container:hover > .overlap {
  color: #efefef;
  padding-top: 30px;
  font-size: 18px;
  background-color: rgba(0, 0, 0, 0.6)
}
.overlay-container:hover > .overlap > p{
  color: #efefef;
}