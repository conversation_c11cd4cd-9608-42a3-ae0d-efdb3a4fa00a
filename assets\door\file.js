

document.getElementById('importFolder').addEventListener('change', async function(e) {
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const jsonData = JSON.parse(e.target.result);
            // 如果menuItemData不为空，则弹窗提示
            if (menuItemData && menuItemData.length > 0) {
                createConfirmDialog(
                    '导入数据',
                    `你有现有数据，确定要导入"${file.name}"数据吗？<br>此操作不可恢复。
                    <br>searchs.json: 搜索框数据
                    <br>navItems.json: 导航栏数据
                    <br>menuItems.json: 菜单数据`,
                    () => {
                      try {
                        if (file.name=='searchs.json') {
                            searchData = jsonData;
                            localStorage.setItem('searchs', JSON.stringify(jsonData));
                        } else if(file.name=='navItems.json'){
                            navItemData = jsonData;
                            localStorage.setItem('navItems', JSON.stringify(jsonData));
                        } else if(file.name=='menuItems.json'){
                            menuItemData = jsonData;
                            localStorage.setItem('menuItems', JSON.stringify(jsonData));
                        }
                        // 显示成功提示
                        renderNavbar();
                        showToast(`导入${file.name}成功`, 'success');
                      } catch (error) {
                        console.error('导入文件时出错:', error);
                        showToast(`导入${file.name}失败`, 'error');
                      }
                    }
                );
            } else {
              try {
                if (file.name=='searchs.json') {
                    searchData = jsonData;
                    localStorage.setItem('searchs', JSON.stringify(jsonData));
                } else if(file.name=='navItems.json'){
                    navItemData = jsonData;
                    localStorage.setItem('navItems', JSON.stringify(jsonData));
                } else if(file.name=='menuItems.json'){
                    menuItemData = jsonData;
                    localStorage.setItem('menuItems', JSON.stringify(jsonData));
                }
                // 显示成功提示
                renderNavbar();
                showToast(`导入${file.name}成功`, 'success');
              } catch (error) {
                console.error('导入文件时出错:', error);
                showToast(`导入${file.name}失败`, 'error');
              }
            }
            console.log('导入的JSON文件:', jsonData);
        } catch (error) {
            console.error('导入失败:', error);
            alert(`导入失败: ${error.message}`);
        }
    }
    reader.readAsText(file);
  });
