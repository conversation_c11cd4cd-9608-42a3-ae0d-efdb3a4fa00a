
  /* Toast提示样式 */
  #toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
  }

  .toast {
    min-width: 280px;
    margin-bottom: 10px;
    padding: 15px 20px;
    border-radius: 12px;
    color: white;
    font-size: 14px;
    display: flex;
    align-items: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), fadeOut 0.5s ease 2.5s forwards;
    overflow: hidden;
    backdrop-filter: blur(5px);
  }

  .toast.success {
    background-color: rgba(76, 175, 80, 0.9);
    border-left: 4px solid #2e7d32;
  }

  .toast.error {
    background-color: rgba(244, 67, 54, 0.9);
    border-left: 4px solid #c62828;
  }

  .toast.info {
    background-color: rgba(33, 150, 243, 0.9);
    border-left: 4px solid #1565c0;
  }

  .toast.warning {
    background-color: rgba(255, 152, 0, 0.9);
    border-left: 4px solid #ef6c00;
  }

  .toast-icon {
    margin-right: 12px;
    font-size: 20px;
  }

  .toast-message {
    flex: 1;
    font-weight: 500;
  }

  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
  }
