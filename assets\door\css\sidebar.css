
.menu-sidebar{
    width: 100%;
    padding: 0px 10px;
}
.menu-sidebar-warp.fixed{
    position: fixed;
    top: 0;
}
.menu-sidebar-go{
    position: relative;
}
.menu-sidebar-go > span{
    position: absolute;
    right: 10px;
    top: 13px;
}
.menu-sidebar-go > span::before{
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    background: #ff6969;
    border-radius: 30px;
    position: absolute;
}
.menu-sidebar-content{
    padding-left: 10px;
}

.menu-sidebar-item{
    cursor: pointer;
    padding: 6px 0;
    letter-spacing: 0.5px;
    width: 80%;
}
.menu-sidebar-item > .menu-sidebar-item-a.active{
    color: #fff;
    background-color: #ff7171;
    border-radius: 6px;
    padding: 6px 8px;
}
.menu-sidebar-item a{
    font-size: 12px;
    padding: 2px 4px;
    transition: 0.1s;
    white-space: nowrap;
    overflow: hidden;
    text-decoration: none;
    color: #3c3c3c;
}

.menu-sidebar-item a:hover{
    color: #fff;
    background-color: #38bcbd;
    border-radius: 6px;
    padding: 6px 8px;
}

.menu-sidebar-item > a.active{
    background-color: #ff7171;
    color: #fff;
}


@media (max-width:319px) {
    .menu-sidebar{
        display: none;
    }
}
@media (min-width:320px) {
    .menu-sidebar{
        display: none;
    }
}
@media (min-width:576px) {
    .menu-sidebar{
        display: none;
    }
}

@media (min-width:768px) {
    .menu-sidebar{
        display: block;
    }
    .menu-sidebar-go{
        display: none;
    }
}


@media (min-width:1200px) {
    .menu-sidebar-item > a{
        font-size: 14px;
    }
    .menu-sidebar-go{
        display: block;
    }
    .menu-sidebar-warp{
        display: grid;
        grid-template-columns: 50px 1fr;
    }
}