<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/pet/1.jpg" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/css/tabs.css">
  <title>赛尔号属性克制表</title>
</head>
<body>
<div id="app">
<div class="header">赛尔号属性克制表</div>
  <div class="container">
    <div class="warp">
      <!-- 上部分的小盒子 -->
      <div class="tabs">
        <div class="tab" :class="attrSingle?'select-tab':''" @click="attrSingle=true">单属性</div>
        <div class="tab" :class="attrSingle?'':'select-tab'" @click="attrSingle=false">双属性</div>
      </div>
      <!-- 下部分的小盒子 -->
      <div class="content">
        <div v-if="attrSingle">
          <div class="box">
            <div v-for="item in single"  @click="toggle1(item)">
              <div class="icon"><img :src="'assets/icon/'+item.icon" width="34" height="31"></div>
              <div class="font">{{item.attr1}}</div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="box">
            <div v-for="item in double"  @click="toggle2(item)">
              <div class="icon"><img :src="'assets/icon/'+item.icon" width="34" height="31"></div>
              <div class="font">{{joint(item.attr1,item.attr2)}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="warp">
      <div class="title">自定义查询</div>
      <div class="box">
        <select v-model="seek.attr1" class="select">
          <option v-for="item in single" :value="item.attr1">{{item.attr1}}</option>
        </select>
        <select v-model="seek.attr2" class="select">
          <option v-for="item in single" :value="item.attr1">{{item.attr1}}</option>
        </select>
        <button @click="toggle2(seek)" class="button">查询</button>
      </div>
    </div>
    <div class="warp">
      <div class="title">{{opt}}系属性相克表</div>
      <div class="desc"><span>攻击效果</span><span>（红色是克制，蓝色是微弱，灰色无伤害，数字代表伤害倍数）</span></div>
      <div class="box">
        <template  v-for="entry in attack" :key="entry.attd">
          <div style="height:70px;" @click="toggle(entry,true)">
            <div class="icon"><img :src="'assets/icon/'+entry.icon" width="34" height="31"></div>
            <div class="font">{{joint(entry.attd,entry.attd2)}}</div>
            <div class="font" :style="bgcolor(entry.resta)" style="color: #D2EEF6;">{{entry.resta}}</div>
          </div>
        </template>
      </div>
      <div class="desc"><span>被攻击效果</span><span>（红色是克制，蓝色是微弱，灰色无伤害，数字代表伤害倍数）</span></div>
      <div class="box">
        <template  v-for="entry in beAttack" :key="entry.attd">
          <div style="height:70px;" @click="toggle(entry,false)">
            <div class="icon"><img :src="'assets/icon/'+entry.icon" width="34" height="31"></div>
            <div class="font">{{joint(entry.atta,entry.atta2)}}</div>
            <div class="font" :style="bgcolor(entry.resta)" style="color: #D2EEF6;">{{entry.resta}}</div>
          </div>
        </template>
      </div>
    </div>
  </div>
  <div class="footer">
    <span>友情链接：</span><a href="https://news.4399.com/seer/ssxxk/">4399赛尔号属性相克表</a>
  </div>
</div>
</body>
<script type="text/javascript" src="assets/data.js"></script>
<script type="text/javascript" src="../../../assets/vue.min.js"></script>
<script>
new Vue({ 
  el: '#app',
  data: {
    attack: singleAtt("草"),
    beAttack: singleBeAtt("草"),
    opt: "草",
    seek:{attr1:"草",attr2:"超能"},
    attrSingle: true,
  },
  //方法
  methods:{
    toggle:function(val,type){
      let val1 = { attr1:(type?val.attd:val.atta),attr2:(type?val.attd2:val.atta2) }
      val1.attr2===undefined?this.toggle1(val1):this.toggle2(val1)
    },
    toggle1:function(val){
      this.opt = val.attr1
      this.attack = singleAtt(val.attr1)
      this.beAttack = singleBeAtt(val.attr1)
    },
    toggle2:function(val){  
      this.opt = val.attr1+val.attr2
      this.attack = doubleAtt(val.attr1,val.attr2)
      this.beAttack = doubleBeAtt(val.attr1,val.attr2)
    },
    joint:function(val1,val2){
      return (val1===undefined?"":val1)+(val2===undefined?"":val2)
    },
    bgcolor:function(val){
      if(val==0) { return {'background-color':'#414141'} }
      else if(val > 1) { return {'background-color':'#B30000'} }
      else { return {'background-color':'#29678E'} }
    }
  },
})


</script>
<style>
.warp{
  min-width: 300px;
  width: 92%;
  margin: 0 auto;
  border: 1px solid #a3d1ec;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}
.tabs{
  width: 100%;
  display: flex;
  white-space: nowrap;
  margin: 0 auto;
  background-color: #e2f4fe;
  justify-content: center;
}

.tabs > .tab{
  width: 150px;
  line-height: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: bolder;
  color: #0093e9;
  cursor: pointer;
}

.tabs > .select-tab{
  background-color: #409eff;
  color: #d2eef6;
}




body{
  font-size: 12px;
}

.container {
  display: grid;
  justify-content: center;
  margin: 0 auto;
}
.single{
  width: 92%;
  margin: 0 auto;
  border: 1px solid #a3d1ec;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}
.title {
  border-bottom: none;
  background-color: #e2f4fe;
  width: 100%;
  line-height: 37px;
  font-size: 20px;
  color: #0093e9;
  font-weight: bold;
  font-family: \5FAE\8F6F\96C5\9ED1;
  text-indent: 1rem;
}
.box{
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  text-align: center;
  padding: 1rem;
  width: calc(100% - 2rem);
}
.box > div{
  width: 50px;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
  margin: 3px;
  background-color: #D2EEF6;
  border-radius: 3px;
  color: #333333;
}

.box > div:hover{
  color: #FFA800;
}
.icon{height: 32px;}
.font{line-height: 18px;}
.desc{
  margin: 1rem;
  background-color: antiquewhite;
  padding: 0.6rem;
}
.desc span:first-child{
  font-size: 1rem;
  font-weight: bolder;
  color: #FFA800;
}
.desc span:nth-child(2){
  font-size: 0.8rem;
  color: #FFA800;
}
.select {
  width: 100px;
  margin-right: 10px;
}
.button{
  width: 80px;
  line-height: 30px;
  font-size: 1rem;
  font-weight: bolder;
}
.header{
  line-height: 5rem;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
}
.footer{
  margin: 2rem auto;
  text-align: center;
  font-size: 0.8rem;
  color: #595959;
}
.footer > a {
  text-decoration: none;
  color: #999999;
}
</style>
</html>