
    
    

.modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 300px;
    max-width: 600px;
    width: 90%;
    background: white;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
    max-height: 90vh; /* 限制最大高度 */
    overflow-y: auto; /* 内容过多时允许滚动 */
  }
 
.modal-container.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.modal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #6a11cb, #2575fc);
}

r
/* 添加模态框背景遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}


.modal-warp{
  width: 100%;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}

.modal-header {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
}

.modal-group {
  width: 100%;
  position: relative;
}

.modal-header .modal-group label {
  font-size: 14px;
  color: rgb(99, 102, 102);
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: #fff;
  transition: all .3s ease;
}

.modal-header .modal-group input,
.modal-header .modal-group textarea {
  padding: 10px;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  outline: 0;
  width: calc(100% - 20px);
  background-color: transparent;
}

.modal-header .modal-group input:placeholder-shown+ label, .modal-header .modal-group textarea:placeholder-shown +label {
  top: 10px;
  background-color: transparent;
}

.modal-header .modal-group input:focus,
.modal-header .modal-group textarea:focus {
  border-color: #3366cc;
}

.modal-header .modal-group input:focus+ label, .modal-header .modal-group textarea:focus +label {
  top: -10px;
  left: 10px;
  background-color: #fff;
  color: #3366cc;
  font-weight: 600;
  font-size: 14px;
}

.modal-header .modal-group textarea {
  resize: none;
  height: 100px;
}

.modal-footer{
  display: flex;
  justify-content: space-between;

}

.modal-footer button {
  width: 48%;
  border: none;
  border-radius: 5px;
  padding: 10px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.modal-footer > button:last-child {
  background-color: rgba(249, 250, 251, 1);
  color: rgba(107, 114, 128, 1);
}
.modal-footer > button:first-child{
  background-color: rgba(59, 130, 246, 1);
  color: rgba(255, 255, 255, 1);
}

.modal-footer > button:last-child:hover {
  background-color: rgb(230, 231, 233);
}
.modal-footer > button:first-child:hover{
  background-color: #185ee0;
}
.modal-close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.modal-close:hover,
.modal-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}
.modal-header .modal-group .error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 8px;
  display: none;
  padding-left: 5px;
}

.modal-header .modal-group.error .error-message {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-group > .modal-select{
  width: 100%;
  border-radius: 5px;
  background-color: transparent;
  margin-bottom: 1rem;
}

.modal-group > .modal-select > span{
  font-size: 12px;
  padding: 6px 12px;
  background-color: #e8e8e8;
  border-radius: 6px;
  margin-right: 10px;
  margin-bottom: 20px;
  cursor: pointer;
}


