<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="../../../assets/icon/avatar.icon" type="image/x-icon"/>
  <title>个人所得税</title>
</head>
<body>
<div id="app" class="container">
  <div class="row">
    <h3>个人所得税</h3>
    <div style="position: relative;">
      <input type="button" value="计 算" onclick="calc()" class="calc-btn" id="calcBtn">
      <input 
        type="number" 
        class="text" 
        id="all_income" 
        maxlength="16" 
        onchange="thousandsSeparator(this)" 
        placeholder="请输入全年收入（如：120000）"
        inputmode="decimal"
        min="0"
        step="0.01"
      >
    </div>
  </div>

  <div class="row">
    <div style="width: 48%; float: left;">
      <input type="number" maxlength="16" class="text" style="padding: 10px;" id="add_deduct" placeholder="专项附加扣除" inputmode="decimal" min="0" step="0.01">
    </div>
    <div style="width: 48%; float: right;">
      <input type="number" maxlength="16" class="text" style="padding: 10px;" id="special_deduct" placeholder="专项扣除（三险一金）" inputmode="decimal" min="0" step="0.01">
    </div>
  </div>
  <div class="row">
    <div style="width: 48%; float: left;">
      <input type="number" maxlength="16" class="text" style="padding: 10px;" id="default_deduct" placeholder="其他扣除项目" inputmode="decimal" min="0" step="0.01">
    </div>
  </div>

  <div class="row">
    <h3>纳税查收入</h3>
    <div style="position: relative;">
      <input type="button" value="计 算" onclick="calcPay()" class="calc-btn" id="calcPayBtn">
      <input 
        type="number" 
        class="text" 
        id="all_pay" 
        maxlength="16" 
        onchange="thousandsSeparator(this)" 
        placeholder="请输入纳税金额"
        inputmode="decimal"
        min="0"
        step="0.01"
      >
    </div>
  </div>

  <div class="row" style="text-align: left;display:none" id="resultBox">
    <table>
      <thead>
        <tr>
          <th>总纳税额：</th>
          <th><span id="totalLimit"></span></th>
          <th></th>
          <th><span id="totalPay"></span></th>
        </tr>
      </thead>
      <tbody id="calcResult">
        <tr>
          <td>{{limitDesc}}</td>
          <td>{{limit}}</td>
          <td>{{taxeRate}}</td>
          <td>{{pay}}</td>
        </tr>
      </tbody>
    </table>
    <div id="afterTax" style="margin-top:1em;font-weight:bold;color:#409EFF;display:none;"></div>
    <button id="copyResultBtn" style="margin-top:1em;display:none;">复制结果</button>
  </div>
  <div class="row" id="evaluate"></div>
  <div class="row" id="taxTableRow" style="margin-top:2em;">
    <details>
      <summary style="cursor:pointer;font-size:1.1em;">查看税率表说明</summary>
      <table style="width:100%;margin-top:1em;font-size:0.95em;background:#f9f9f9;">
        <thead>
          <tr>
            <th>级数</th>
            <th>应纳税所得额</th>
            <th>税率</th>
            <th>速算扣除数</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>1</td><td>不超过36,000元</td><td>3%</td><td>0</td></tr>
          <tr><td>2</td><td>36,000~144,000元</td><td>10%</td><td>2,520</td></tr>
          <tr><td>3</td><td>144,000~300,000元</td><td>20%</td><td>16,920</td></tr>
          <tr><td>4</td><td>300,000~420,000元</td><td>25%</td><td>31,920</td></tr>
          <tr><td>5</td><td>420,000~660,000元</td><td>30%</td><td>52,920</td></tr>
          <tr><td>6</td><td>660,000~960,000元</td><td>35%</td><td>85,920</td></tr>
          <tr><td>7</td><td>超过960,000元</td><td>45%</td><td>181,920</td></tr>
        </tbody>
      </table>
    </details>
  </div>
</div>
</body>
<script src='../../../assets/decimal.js'></script>
<script>
  var taxes = [
    {seq:1,end:36000,rate:0.03,fast:1080},
    {seq:2,start:36000,end:144000,rate:0.10,fast:11880},
    {seq:3,start:144000,end:300000,rate:0.20,fast:43080},
    {seq:4,start:300000,end:420000,rate:0.25,fast:73080},
    {seq:5,start:420000,end:660000,rate:0.30,fast:145080},
    {seq:6,start:660000,end:960000,rate:0.35,fast:250080},
    {seq:7,start:960000,rate:0.45,fast:Number.MAX_VALUE}
  ]

  var freeTax = 60000

  taxes.forEach(function(taxe) {
    if(taxe.start===undefined){
      taxe["desc"] = "不超过"+taxe.end+"元的部分"
    } else if(taxe.end===undefined){
      taxe["desc"] = "超过"+taxe.start+"元的部分"
    } else {
      taxe["desc"] = "超过"+taxe.start+"元至"+taxe.end+"元的部分"
    }
  });

  var evaluate1 = ['一点税都不交，也太小气了吧！！','没交税白嫖呢','祝你明年能缴上税','大穷逼']
  var evaluate2 = ['才纳这么点税，也太苛刻了']
  var evaluate3 = ['我也想交这么多税','也就比我多交那么一丢丢吧']
  var evaluate3 = ['总有一天我也要交这么多税','羡慕了！！']
  var evaluate5 = ['哇，大佬分点钱给我','我不羡慕我一点都不羡慕','请收下我的收款码','也就比我多交那么亿丢丢吧','别太离谱']

  function evaluate(pay){
    let eval
    if(pay<1){
      eval = evaluate1[Math.floor(Math.random() * evaluate1.length)] 
    } else if(pay<1080){
      eval = evaluate2[Math.floor(Math.random() * evaluate2.length)] 
    } else if(pay<43080){
      eval = evaluate3[Math.floor(Math.random() * evaluate3.length)] 
    } else if(pay<250080){
      eval = evaluate4[Math.floor(Math.random() * evaluate4.length)] 
    } else {
      eval = evaluate5[Math.floor(Math.random() * evaluate5.length)] 
    }
    $("evaluate").innerText = eval
    $('evaluate').style.display = 'block';// 以块级样式显示
  }
  
  var calcDome = $('calcResult').innerHTML;

  // console.log(calcDome)

  function $(id){
    return document.getElementById(id);
  }

  // 优化：输入时自动去除千分位，失焦时加上
  function thousandsSeparator(e){
    let val = e.value.replace(/,/g,'');
    if (e === document.activeElement) {
      // 输入时不加千分位
      e.value = val;
    } else {
      e.value = numberWithCommas(val);
    }
  }
  // 失焦时加千分位
  var inputs = document.querySelectorAll('input[type="number"],input.text');
  inputs.forEach(function(input){
    input.addEventListener('focus', function(){ thousandsSeparator(this); });
    input.addEventListener('blur', function(){ thousandsSeparator(this); });
  });

  // 优化：输入校验，非数字或为空时提示
  function val(id){
    let v = $(id).value.replace(/,/g,'');
    if(v === undefined || v == null || v.trim() === '' || isNaN(Number(v))){
      $(id).style.borderColor = 'red';
      setTimeout(()=>{$(id).style.borderColor='rgb(79, 54, 74)';}, 1200);
      return 0.00;
    }
    return v;
  }

  function calc(){
    var list = new Array()
    var income = Number(val("all_income"))
    var addDeduct = Number(val("add_deduct"))
    var specialDeduct = Number(val("special_deduct"))
    var defaultDeduct = Number(val("default_deduct"))
    var tax = new Decimal(income).minus(freeTax).minus(addDeduct).minus(specialDeduct).minus(defaultDeduct)
    list.push({limitDesc:"基础减除费用额度",taxeRate:'',limit:freeTax,pay:0})
    if(addDeduct!=0){
      list.push({limitDesc:"专项附加扣除",taxeRate:'',limit:addDeduct,pay:0})
    }
    if(specialDeduct!=0){
      list.push({limitDesc:"专项扣除（三险一金）",taxeRate:'',limit:specialDeduct,pay:0})
    }
    if(defaultDeduct!=0){
      list.push({limitDesc:"其他扣除项目",taxeRate:'',limit:defaultDeduct,pay:0})
    }
    // console.log(Number(tax))
    for (var i = 0; i < taxes.length; i++) {
      let taxeStart = taxes[i].start
      taxeStart = taxeStart===undefined?0:taxeStart
      let taxeEnd = taxes[i].end
      let taxeRate = taxes[i].rate
      if(tax > taxeStart){
        let limit = (tax-taxeEnd < 0 || taxeEnd===undefined?tax:taxeEnd)-taxeStart
        // console.log("limit="+limit)
        // console.log({ limitDesc:taxes[i].desc,limit:limit,taxeRate:(taxeRate*100)+"%",pay:limit*taxeRate})
        list.push({limitDesc:taxes[i].desc,limit:limit,taxeRate:(taxeRate*100)+"%",pay:new Decimal(limit).times(taxeRate)})
      }
    }
    
    var calcHtml = ""
    var total = 0
    var totalLimit = 0
    // console.log(list)
    if(tax < 0){
      calcHtml = calcDome
        .replace('{{limitDesc}}', "你还需"+Math.abs(tax)+"元才能达到缴税标准")
        .replace('{{limit}}', '')
        .replace('{{taxeRate}}', '')
        .replace('{{pay}}', 0)
      $('afterTax').style.display = 'none';
      $('copyResultBtn').style.display = 'none';
    } else {
      list.forEach(function(value) {
        total += Number(value.pay)
        totalLimit += Number(value.limit)
        calcHtml += calcDome
          .replace('{{limitDesc}}', value.limitDesc)
          .replace('{{limit}}', value.limit)
          .replace('{{taxeRate}}', value.taxeRate)
          .replace('{{pay}}', value.pay)
      });
      // 税后收入
      let afterTax = income - total;
      $('afterTax').innerHTML = "税后收入：<span style='color:#f56c6c;font-size:1.2em;'>" + numberWithCommas(afterTax.toFixed(2)) + " 元</span>";
      $('afterTax').style.display = '';
      $('copyResultBtn').style.display = '';
    }
    $('resultBox').style.display = 'block';
    $('calcResult').innerHTML = calcHtml
    $('totalPay').innerText=total + '元'
    $('totalLimit').innerText= ''
    evaluate(total)
  }

  // 复制结果按钮
  document.getElementById('copyResultBtn').onclick = function(){
    let resultText = document.getElementById('calcResult').innerText + "\n" + document.getElementById('afterTax').innerText;
    navigator.clipboard.writeText(resultText).then(function(){
      let tip = document.createElement('div');
      tip.textContent = '已复制!';
      tip.style.cssText = 'position:fixed;top:30%;left:50%;transform:translate(-50%,-50%);background:#333;color:#ffd700;padding:10px 24px;border-radius:8px;z-index:9999;font-size:1.2rem;opacity:0.95;';
      document.body.appendChild(tip);
      setTimeout(()=>{ tip.remove(); }, 1200);
    });
  };

  var evaluate1 = ['一点税都不交，也太小气了吧！！','没交税白嫖呢','祝你明年能缴上税','大穷逼']
  var evaluate2 = ['才纳这么点税，也太苛刻了']
  var evaluate3 = ['我也想交这么多税','也就比我多交那么一丢丢吧']
  var evaluate3 = ['总有一天我也要交这么多税','羡慕了！！']
  var evaluate5 = ['哇，大佬分点钱给我','我不羡慕我一点都不羡慕','请收下我的收款码','也就比我多交那么亿丢丢吧','别太离谱']

  function evaluate(pay){
    let eval
    if(pay<1){
      eval = evaluate1[Math.floor(Math.random() * evaluate1.length)] 
    } else if(pay<1080){
      eval = evaluate2[Math.floor(Math.random() * evaluate2.length)] 
    } else if(pay<43080){
      eval = evaluate3[Math.floor(Math.random() * evaluate3.length)] 
    } else if(pay<250080){
      eval = evaluate4[Math.floor(Math.random() * evaluate4.length)] 
    } else {
      eval = evaluate5[Math.floor(Math.random() * evaluate5.length)] 
    }
    $("evaluate").innerText = eval
    $('evaluate').style.display = 'block';// 以块级样式显示
  }
  
  var calcDome = $('calcResult').innerHTML;

  // console.log(calcDome)

  function $(id){
    return document.getElementById(id);
  }

  function numberWithCommas(x) {
    return x.toString().replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
  }

  function thousandsSeparator(e){
    let val = e.value.replaceAll(',','')
    e.value = numberWithCommas(val)
  }

  var inputList = document.getElementsByTagName('input')
  for (var i = 0; i < inputList.length; i++) {
    inputList[i].addEventListener("keydown", function(event) {
      // 获取按下的键值
      var keyCode = event.which || event.keyCode;
      // 利用正则表达式判断是否为数字或者删除、后退等特定操作
      if (!((keyCode >= 48 && keyCode <= 57) || (keyCode === 8 || keyCode === 9 || keyCode === 37 || keyCode === 39 || keyCode === 110 || keyCode === 190))){
          // 若不符合条件，则阻止默认事件（即输入）发生
          event.preventDefault();
      }
    });
    inputList[i].onkeyup = function () {
      changeNum(this);
    }

  }

  function changeNum(obj) {
    //如果用户第一位输入的是小数点，则重置输入框内容
    if (obj.value != '' && obj.value.substr(0, 1) == '.') {
      obj.value = "";
    }
    obj.value = obj.value.replace(/^0*(0\.|[1-9])/, '$1');//粘贴不生效
    obj.value = obj.value.replace(/[^\d.,]/g, "");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数
    if (obj.value.indexOf(".") < 0 && obj.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      if (obj.value.substr(0, 1) == '0' && obj.value.length == 2) {
        obj.value = obj.value.substr(1, obj.value.length);
      }
    }
  }

</script>
<style>
  .container {
    text-align: center;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .row{width: 96%; max-width: 600px;}
  .row>h3{ color: rgb(79, 54, 74);}

  .text{
    width: 100%;
    height: 3rem;
    font-size: 1rem;
    padding: 10px 80px 10px 10px;
    border-color: rgb(79, 54, 74);
    border-width: 5px;
    border-radius: 1rem;
    box-sizing: border-box;
    margin-bottom: 1rem;
  }

  .calc-btn{
    position: absolute;
    right: 0;
    height: 3rem;
    width: 80px;
    border-width: 5px;
    border-radius:0 1rem 1rem 0;
    border-color: rgb(79, 54, 74);
    background-color: rgb(181, 150, 181);
    outline: none;  /*去掉点击按钮后的边框*/
    cursor: pointer;
    font-weight: bold;
  }

  #resultBox >table{
    width: 100%;
  }

  #evaluate {
   font-size: 0.8rem;
   color:rgb(181,150,181);
   display:none;
   margin: 2rem auto;
  }
  table{
    /* border: 5px solid rgb(79, 54, 74);
    border-radius: 1rem;
    padding: 1rem; */
    border-collapse: collapse;
  }
  td{
    border-top: 2px solid rgb(79, 54, 74,0.2);
    padding: 4px 0;
  }
  th{
    padding: 4px 0;
  }
  tr > td:nth-last-child(1),th:nth-last-child(1){
    text-align: right;
  }
  #afterTax {
    margin-top: 1em;
    font-weight: bold;
    color: #409EFF;
    display: none;
  }
  #copyResultBtn {
    margin-top: 1em;
    display: none;
    background: #ffd700;
    color: #222;
    border: none;
    border-radius: 6px;
    padding: 8px 18px;
    font-size: 1em;
    cursor: pointer;
    transition: background 0.2s;
  }
  #copyResultBtn:hover {
    background: #faad14;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }
</style>
</html>
































