*{margin: 0px;padding: 0px;}
#app{width: 100%;min-width: 260px;height: 100vh;overflow: hidden;}
.cartoon{height: 50vh;text-align: center;
  display: flex;justify-content:center;align-items: center;}
.panel{height: 50vh;padding: 0px 3%;display: flex;}

.h3-font{
  line-height: 2rem;
}
#record{
  width: 100%;
  font-size: 12px;
  padding: 6px;
  height: 90%;
  border: none;
  resize:none;
}
.record{
  height: 90%;
  width: 70%;
}

/* 禁止选择文字 */
div{
  -moz-user-select:none;/*火狐*/
  -webkit-user-select:none;/*webkit浏览器*/
  -ms-user-select:none;/*IE10*/
  -khtml-user-select:none;/*早期浏览器*/
  user-select:none; 
}

 /*手机点击有红框，去红框*/
 a,button,input,div{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  -moz-tap-highlight-color:rgba(0, 0, 0, 0);
  -ms-tap-highlight-color:rgba(0, 0, 0, 0);
  -o-tap-highlight-color:rgba(0, 0, 0, 0);/*解决移动端点击显示背景框问题*/
}

#knapsack{
  width: 30%;
  height: 100%;
}


.pet-select{
  width: 20%;
  border-radius: 50%;
  cursor: pointer;
  margin: 0 3%;
}

