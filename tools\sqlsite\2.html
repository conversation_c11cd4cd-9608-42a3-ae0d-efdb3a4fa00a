<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>SQL展示器</title>
  <style>
    body { font-family: 'Segoe UI', Arial, sans-serif; background: #f7f9fb; margin: 0; padding: 0; }
    .container { max-width: 900px; margin: 2rem auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 12px #0001; padding: 2rem; }
    .sql-input { width: 100%; min-height: 120px; font-size: 15px; border-radius: 6px; border: 1px solid #d0d7de; padding: 1em; margin-bottom: 1.2em; }
    .btn { background: #4f8cff; color: #fff; border: none; border-radius: 6px; padding: 8px 22px; font-size: 15px; cursor: pointer; margin-bottom: 1.5em; }
    .btn:hover { background: #357ae8; }
    .result-table { width: 100%; border-collapse: collapse; margin-top: 1.5em; }
    .result-table th, .result-table td { border: 1px solid #e3e8f7; padding: 8px 10px; text-align: left; }
    .result-table th { background: #f0f6ff; color: #357ae8; }
    .result-table tr:nth-child(even) { background: #f8fafd; }
    .highlight { color: #4f8cff; font-weight: bold; }
    .type { color: #27ae60; }
    .nullable { color: #e67e22; }
    .pk { color: #e74c3c; }
    .sql-tabs { margin-top: 2em; }
    .sql-tabs button { margin-right: 10px; padding: 6px 18px; border-radius: 6px; border: 1px solid #d0d7de; background: #f0f6ff; color: #357ae8; cursor: pointer; }
    .sql-tabs button.active { background: #4f8cff; color: #fff; border-color: #4f8cff; }
    .sql-output { background: #f8fafd; border-radius: 6px; padding: 1em; margin-top: 1em; font-family: 'Consolas', monospace; font-size: 15px; white-space: pre-wrap; }
    .error-tip { color: #e67e22; font-size: 14px; margin-bottom: 1em; }
    .table-edit-btns { margin-bottom: 1em; }
    .table-edit-btns button { margin-right: 10px; }
    .add-col-btn { color: #357ae8; background: #eaf3ff; border: 1px solid #d0d7de; border-radius: 4px; padding: 2px 10px; cursor: pointer; }
    .del-col-btn { color: #e74c3c; background: #fff0f0; border: 1px solid #f5c6cb; border-radius: 4px; padding: 2px 10px; cursor: pointer; }
  </style>
</head>
<body>
  <div class="container">
    <h2>SQL 创建语句分析展示器</h2>
    <div class="table-edit-btns">
      <button class="btn" onclick="generateSQLFromTable()">创建表生成SQL</button>
      <button class="btn" onclick="analyzeSQL()">SQL生成表</button>
    </div>
    <textarea class="sql-input" id="sqlInput" placeholder="粘贴或输入SQL建表语句，支持MySQL/SQLServer/高斯/Oracle等"></textarea>
    <div id="result"></div>
    <div class="sql-tabs" id="sqlTabs" style="display:none;">
      <button onclick="showSQL('mysql')" id="tab-mysql" class="active">MySQL</button>
      <button onclick="showSQL('oracle')" id="tab-oracle">Oracle</button>
      <button onclick="showSQL('sqlserver')" id="tab-sqlserver">SQLServer</button>
      <button onclick="showSQL('gauss')" id="tab-gauss">高斯</button>
    </div>
    <div class="sql-output" id="sqlOutput" style="display:none;"></div>
  </div>
  <script>
let sqlVariants = {};
let editableColumns = [];
let editableTableName = "my_table";
const typeOptions = [
  "VARCHAR", "INT", "BIGINT", "NUMERIC", "DATE", "DATETIME", "TIMESTAMP", "TEXT"
];
const constraintOptions = [
  { value: "", label: "无" },
  { value: "PRIMARY KEY", label: "主键" },
  { value: "NOT NULL", label: "非空" },
  { value: "UNIQUE", label: "唯一" },
  { value: "AUTO_INCREMENT", label: "自增" }
];

function autoCorrectSQL(sql) {
  let corrected = sql.trim();
  if (!/;\s*$/.test(corrected)) corrected += ';';
  corrected = corrected.replace(/creat\s+table/i, 'CREATE TABLE');
  corrected = corrected.replace(/varchar2?/ig, 'VARCHAR');
  corrected = corrected.replace(/number/ig, 'NUMERIC');
  corrected = corrected.replace(/（/g, '(').replace(/）/g, ')');
  return corrected;
}

// SQL生成表
function analyzeSQL() {
  const sqlRaw = document.getElementById('sqlInput').value;
  const resultDiv = document.getElementById('result');
  const sqlTabs = document.getElementById('sqlTabs');
  const sqlOutput = document.getElementById('sqlOutput');
  sqlTabs.style.display = 'none';
  sqlOutput.style.display = 'none';
  sqlVariants = {};
  editableColumns = [];

  if (!sqlRaw.trim()) {
    resultDiv.innerHTML = '<p style="color:#e74c3c;">请输入SQL建表语句！</p>';
    return;
  }

  let sql = autoCorrectSQL(sqlRaw);
  let errorTip = '';
  if (sql !== sqlRaw) {
    errorTip = '<div class="error-tip">已自动纠正部分SQL格式或关键字，请检查。</div>';
  }

  const tableMatch = sql.match(/CREATE\s+TABLE\s+([`"\[]?)(\w+)\1\s*\(([\s\S]+?)\)[^)]*;/i)
    || sql.match(/CREATE\s+TABLE\s+([`"\[]?)(\w+)\1\s*\(([\s\S]+?)\)[^)]*$/i);
  if (!tableMatch) {
    resultDiv.innerHTML = errorTip + '<p style="color:#e74c3c;">未识别到有效的CREATE TABLE语句。</p>';
    return;
  }
  const tableName = tableMatch[2];
  editableTableName = tableName;
  const fieldsStr = tableMatch[3];
  const lines = fieldsStr.split(/,(?![^\(\[]*[\]\)])/).map(f => f.trim()).filter(f => f);
  const fieldLines = lines.filter(line => !/^(PRIMARY|UNIQUE|KEY|CONSTRAINT|INDEX|FOREIGN|CHECK)\b/i.test(line));
  let html = errorTip + `<h3>表名：<input id="editable-table-name" value="${tableName}" style="font-size:1.1em;width:auto;min-width:80px;max-width:200px;border:1px solid #d0d7de;border-radius:4px;padding:2px 6px;" /></h3>`;
  html += `<table class="result-table" id="editable-table"><tr><th>字段名</th><th>类型</th><th>约束</th><th>操作</th></tr>`;
  let columns = [];
  fieldLines.forEach((field, idx) => {
    const m = field.match(/^([`"\[]?)(\w+)\1\s+([A-Z0-9_\(\), ]+)(.*)$/i);
    if (m) {
      const name = m[2];
      const type = m[3].trim();
      let constraint = '';
      let constraintVal = "";
      if (/PRIMARY\s+KEY/i.test(field)) { constraint += '<span class="pk">主键</span> '; constraintVal = "PRIMARY KEY"; }
      else if (/NOT\s+NULL/i.test(field)) { constraint += '<span class="nullable">非空</span> '; constraintVal = "NOT NULL"; }
      else if (/UNIQUE/i.test(field)) { constraint += '唯一 '; constraintVal = "UNIQUE"; }
      else if (/AUTO_INCREMENT|IDENTITY|SERIAL/i.test(field)) { constraint += '自增 '; constraintVal = "AUTO_INCREMENT"; }
      html += `<tr>
        <td><input class="editable-col-name" data-idx="${idx}" value="${name}" style="width:110px;max-width:180px;" /></td>
        <td>
          <select class="editable-col-type" data-idx="${idx}" style="width:120px;max-width:200px;">
            ${typeOptions.map(opt => `<option value="${opt}" ${type.toUpperCase().startsWith(opt) ? 'selected' : ''}>${opt}</option>`).join('')}
          </select>
        </td>
        <td>
          <select class="editable-col-constraint" data-idx="${idx}" style="width:110px;">
            ${constraintOptions.map(opt => `<option value="${opt.value}" ${constraintVal === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
          </select>
        </td>
        <td><button class="del-col-btn" data-idx="${idx}" title="删除">删除</button></td>
      </tr>`;
      columns.push({
        name,
        type,
        rawType: type,
        constraint: constraintVal
      });
    }
  });
  html += `</table><button class="add-col-btn" onclick="addColumn()">+ 添加字段</button>`;
  resultDiv.innerHTML = html;
  editableColumns = columns;
  bindEditableEvents();
  sqlVariants = generateSQLVariants(tableName, columns, []);
  sqlTabs.style.display = 'block';
  showSQL('mysql');
}

// 表生成SQL
function generateSQLFromTable() {
  const resultDiv = document.getElementById('result');
  let tableName = editableTableName;
  let columns = editableColumns;
  // 若当前页面有表格，取表单内容
  const tableNameInput = document.getElementById('editable-table-name');
  if (tableNameInput) tableName = tableNameInput.value;
  const colInputs = document.querySelectorAll('.editable-col-name');
  const typeInputs = document.querySelectorAll('.editable-col-type');
  const constraintInputs = document.querySelectorAll('.editable-col-constraint');
  columns = [];
  for (let i = 0; i < colInputs.length; i++) {
    columns.push({
      name: colInputs[i].value,
      type: typeInputs[i].value,
      rawType: typeInputs[i].value,
      constraint: constraintInputs[i].value
    });
  }
  editableColumns = columns;
  editableTableName = tableName;
  // 渲染表格
  let html = `<h3>表名：<input id="editable-table-name" value="${tableName}" style="font-size:1.1em;width:auto;min-width:80px;max-width:200px;border:1px solid #d0d7de;border-radius:4px;padding:2px 6px;" /></h3>`;
  html += `<table class="result-table" id="editable-table"><tr><th>字段名</th><th>类型</th><th>约束</th><th>操作</th></tr>`;
  columns.forEach((col, idx) => {
    html += `<tr>
      <td><input class="editable-col-name" data-idx="${idx}" value="${col.name}" style="width:110px;max-width:180px;" /></td>
      <td>
        <select class="editable-col-type" data-idx="${idx}" style="width:120px;max-width:200px;">
          ${typeOptions.map(opt => `<option value="${opt}" ${col.type.toUpperCase().startsWith(opt) ? 'selected' : ''}>${opt}</option>`).join('')}
        </select>
      </td>
      <td>
        <select class="editable-col-constraint" data-idx="${idx}" style="width:110px;">
          ${constraintOptions.map(opt => `<option value="${opt.value}" ${col.constraint === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
        </select>
      </td>
      <td><button class="del-col-btn" data-idx="${idx}" title="删除">删除</button></td>
    </tr>`;
  });
  html += `</table><button class="add-col-btn" onclick="addColumn()">+ 添加字段</button>`;
  resultDiv.innerHTML = html;
  bindEditableEvents();
  sqlVariants = generateSQLVariants(tableName, columns, []);
  document.getElementById('sqlTabs').style.display = 'block';
  showSQL('mysql');
}

function addColumn() {
  editableColumns.push({ name: '', type: 'VARCHAR', rawType: 'VARCHAR', constraint: '' });
  generateSQLFromTable();
}

function bindEditableEvents() {
  // 表名编辑
  const tableNameInput = document.getElementById('editable-table-name');
  if (tableNameInput) {
    tableNameInput.addEventListener('input', function() {
      editableTableName = tableNameInput.value;
      updateSQLVariants();
    });
  }
  // 字段名编辑
  document.querySelectorAll('.editable-col-name').forEach(input => {
    input.addEventListener('input', function() {
      const idx = +input.dataset.idx;
      editableColumns[idx].name = input.value;
      updateSQLVariants();
    });
  });
  // 字段类型编辑
  document.querySelectorAll('.editable-col-type').forEach(select => {
    select.addEventListener('change', function() {
      const idx = +select.dataset.idx;
      editableColumns[idx].type = select.value;
      editableColumns[idx].rawType = select.value;
      updateSQLVariants();
    });
  });
  // 字段约束编辑
  document.querySelectorAll('.editable-col-constraint').forEach(select => {
    select.addEventListener('change', function() {
      const idx = +select.dataset.idx;
      editableColumns[idx].constraint = select.value;
      updateSQLVariants();
    });
  });
  // 删除字段
  document.querySelectorAll('.del-col-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const idx = +btn.dataset.idx;
      editableColumns.splice(idx, 1);
      generateSQLFromTable();
    });
  });
}

function updateSQLVariants() {
  sqlVariants = generateSQLVariants(editableTableName, editableColumns, []);
  // 保持当前tab
  const activeTab = document.querySelector('.sql-tabs button.active');
  if (activeTab) {
    showSQL(activeTab.id.replace('tab-', ''));
  }
}

function showSQL(type) {
  document.getElementById('sqlOutput').style.display = 'block';
  ['mysql', 'oracle', 'sqlserver', 'gauss'].forEach(t => {
    document.getElementById('tab-' + t).classList.remove('active');
  });
  document.getElementById('tab-' + type).classList.add('active');
  document.getElementById('sqlOutput').textContent = sqlVariants[type] || '-- 暂无该类型SQL --';
}

function generateSQLVariants(table, columns, pkFields) {
  const typeMap = {
    mysql:    { 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INT', 'DATE': 'DATE', 'DATETIME': 'DATETIME', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'INT', 'BIGINT': 'BIGINT', 'TEXT': 'TEXT' },
    oracle:   { 'VARCHAR': 'VARCHAR2(255)', 'NUMERIC': 'NUMBER', 'DATE': 'DATE', 'DATETIME': 'DATE', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'NUMBER', 'BIGINT': 'NUMBER', 'TEXT': 'CLOB' },
    sqlserver:{ 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INT', 'DATE': 'DATE', 'DATETIME': 'DATETIME', 'TIMESTAMP': 'DATETIME', 'INT': 'INT', 'BIGINT': 'BIGINT', 'TEXT': 'TEXT' },
    gauss:    { 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INTEGER', 'DATE': 'DATE', 'DATETIME': 'TIMESTAMP', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'INTEGER', 'BIGINT': 'BIGINT', 'TEXT': 'TEXT' }
  };
  let result = {};
  ['mysql', 'oracle', 'sqlserver', 'gauss'].forEach(db => {
    let lines = [];
    columns.forEach(col => {
      if (!col.name) return;
      let t = col.rawType.toUpperCase();
      Object.keys(typeMap[db]).forEach(k => {
        t = t.replace(new RegExp(k, 'g'), typeMap[db][k]);
      });
      let line = `  ${col.name} ${t}`;
      if (col.constraint === 'PRIMARY KEY') {
        // 主键单独处理
      } else {
        if (col.constraint === 'NOT NULL') line += ' NOT NULL';
        if (col.constraint === 'UNIQUE') line += ' UNIQUE';
        if (col.constraint === 'AUTO_INCREMENT') {
          if (db === 'mysql') line += ' AUTO_INCREMENT';
          if (db === 'sqlserver') line += ' IDENTITY(1,1)';
        }
      }
      lines.push(line);
    });
    // 主键
    let pkCols = columns.filter(col => col.constraint === 'PRIMARY KEY').map(col => col.name);
    if (pkCols.length) {
      lines.push(`  PRIMARY KEY (${pkCols.join(', ')})`);
    }
    result[db] = `CREATE TABLE ${table} (\n${lines.join(',\n')}\n);`;
  });
  return result;
}
  </script>
  <style>
.result-table input, .result-table select {
  box-sizing: border-box;
  width: 100%;
  min-width: 60px;
  max-width: 200px;
  font-size: 15px;
  padding: 2px 6px;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  background: #f8fafd;
}
#editable-table-name {
  min-width: 60px;
  max-width: 200px;
  width: auto;
  box-sizing: border-box;
}
.add-col-btn, .del-col-btn {
  margin-top: 4px;
}
  </style>
</body>
</html>