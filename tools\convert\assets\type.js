let convertTypes = [
  { key: 'rmb', label: '数值转银行大写' },
  { key: 'thousand', label: '千位分隔符' },
  { key: 'upper', label: '小写转大写' },
  { key: 'lower', label: '大写转小写' },
  { key: 'swap', label: '大小写互转' },
  { key: 'pinyinFull', label: '文字转全写拼音' },
  { key: 'pinyinCamel', label: '文字转简写拼音' },
  { key: 'num', label: '提取数字' },
  { key: 'hanzi', label: '提取汉字' },
  { key: 'alpha', label: '提取字母' },
  { key: 'stat', label: '统计' }
];
let selectedTypes = new Set(convertTypes.map(t=>t.key));
function renderTypeSelector() {
  let selBox = document.getElementById('typeSelector');
  if (!selBox) {
    selBox = document.createElement('div');
    selBox.id = 'typeSelector';
    selBox.className = 'row';
    document.getElementById('app').insertBefore(selBox, document.getElementById('app').children[1]);
  }
  selBox.innerHTML = convertTypes.map(t =>
    `<label style="margin:0 8px 0 0;font-size:13px;">
      <input type="checkbox" value="${t.key}" checked onchange="toggleType('${t.key}')">${t.label}
    </label>`
  ).join('');
}
window.toggleType = function(key) {
  if (selectedTypes.has(key)) selectedTypes.delete(key);
  else selectedTypes.add(key);
};