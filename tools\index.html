<!DOCTYPE html>
<html lang="zh-CN">
<head>
   <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8" />
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" /><!-- 兼容标签 -->
  <link rel="shortcut icon" href="door/assets/icon/logo.png" type="image/x-icon" />
  <title>我的首页</title>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background: #222;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      font-family: monospace, 'Press Start 2P', cursive;
      user-select: none;
    }
    body {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: flex-end;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(to top, #222 60%, #5c94fc 100%);
    }
    .score-panel {
      position: fixed;
      top: 16px; left: 16px;
      color: #ffd700;
      font-size: 1.1rem;
      text-shadow: 2px 2px #222;
      z-index: 10;
      font-family: inherit;
      background: rgba(34,34,34,0.7);
      padding: 8px 20px;
      border-radius: 10px;
      border: 2px solid #ffd700;
      min-width: 180px;
      line-height: 1.7;
      user-select: text;
    }
    .game-container {
      position: relative;
      width: 100vw;
      height: 240px;
      background: transparent;
      overflow: hidden;
      margin: 0;
      z-index: 1;
      /* 无边框 */
      border: none;
      box-shadow: none;
    }
    .ground {
      position: absolute;
      left: 0; bottom: 0;
      width: 100vw; height: 48px;
      background: repeating-linear-gradient(
        to right,
        #b97a56 0 32px,
        #d9a066 32px 64px
      );
      box-shadow: 0 -2px 0 #8b5c2b inset;
      z-index: 2;
    }
    .player {
      position: absolute;
      left: 80px; bottom: 48px;
      width: 40px; height: 40px;
      background:
        linear-gradient(90deg, #ffb347 60%, #ff5722 100%);
      box-shadow: 0 0 0 4px #222, 2px 4px 0 0 #0008;
      image-rendering: pixelated;
      border-radius: 6px;
      border: 2px solid #fff3;
      animation: player-bounce 0.5s infinite alternate;
      z-index: 3;
    }
    @keyframes player-bounce {
      0% { filter: brightness(1);}
      100% { filter: brightness(1.15);}
    }
    .obstacle {
      position: absolute;
      bottom: 48px;
      width: 32px; height: 48px;
      background:
        linear-gradient(90deg, #ffe066 60%, #ffd700 100%);
      box-shadow: 0 0 0 3px #222, 2px 4px 0 0 #0006;
      image-rendering: pixelated;
      border-radius: 4px;
      border: 2px solid #fff3;
      animation: obs-bounce 0.7s infinite alternate;
      z-index: 3;
    }
    @keyframes obs-bounce {
      0% { filter: brightness(1);}
      100% { filter: brightness(1.2);}
    }
    .game-over {
      position: absolute;
      width: 100vw;
      top: 80px;
      text-align: center;
      color: #fff;
      font-size: 1.5rem;
      text-shadow: 2px 2px #222;
      z-index: 5;
      display: none;
      font-family: inherit;
      pointer-events: none;
    }
    .restart-btn {
      margin: 32px 0 48px 0;
      padding: 16px 48px;
      background: #ffd700;
      color: #222;
      border: 4px solid #222;
      font-family: inherit;
      font-size: 1.2rem;
      border-radius: 12px;
      cursor: pointer;
      box-shadow: 4px 4px 0 #000;
      transition: box-shadow 0.1s, transform 0.1s;
      outline: none;
      user-select: none;
      z-index: 10;
      display: block;
      margin-left: auto;
      margin-right: auto;
    }
    .restart-btn:active {
      box-shadow: 2px 2px 0 #000;
      transform: scale(0.96);
    }
  </style>
</head>
<body>
  <div class="score-panel" id="scorePanel">
    分数: 0<br>
    最高分: 0<br>
    平均分: 0
  </div>
  <div class="game-container" id="game">
    <div class="player" id="player"></div>
    <div class="ground"></div>
    <div class="game-over" id="gameOver">游戏结束<br>按空格或点击页面重新开始</div>
  </div>
  <div style="display:flex; justify-content:center; gap:24px; margin:32px 0 48px 0;">
    <button class="restart-btn" id="restartBtn">任意门</button>
    <button class="restart-btn" id="resetScoreBtn">百宝箱</button>
  </div>
  <script>
    const player = document.getElementById('player');
    const game = document.getElementById('game');
    const gameOverEl = document.getElementById('gameOver');
    const restartBtn = document.getElementById('restartBtn');
    const resetScoreBtn = document.getElementById('resetScoreBtn');
    const scorePanel = document.getElementById('scorePanel');
    let isJumping = false, velocity = 0, gravity = 1.4, jumpPower = 18;
    let obstacles = [];
    let score = 0, running = true, speed = 6;
    let scores = JSON.parse(localStorage.getItem('pixelGameScores') || '[]');
    let highScore = scores.length ? Math.max(...scores) : 0;
    let avgScore = scores.length ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1) : 0;
    let animationId = null;

    function updateScorePanel() {
      scorePanel.innerHTML =
        `分数: ${score}<br>最高分: ${highScore}<br>平均分: ${avgScore}`;
    }

    function jump() {
      if (!isJumping && running) {
        isJumping = true;
        velocity = jumpPower;
      }
    }

    // 优化：移动端用 touchstart，PC端用 mousedown
    function handleJumpEvent(e) {
      if (e.target === restartBtn || e.target === resetScoreBtn) return;
      if (!running) restart();
      else jump();
    }

    // PC端
    document.addEventListener('mousedown', handleJumpEvent);
    // 移动端
    document.addEventListener('touchstart', function(e) {
      // 只对游戏区阻止默认，按钮不阻止
      if (
        e.target === restartBtn ||
        e.target === resetScoreBtn
      ) {
        // 让按钮跳转正常
        return;
      }
      handleJumpEvent(e);
      e.preventDefault();
    }, {passive: false});

    document.addEventListener('keydown', e => {
      if (e.code === 'Space') {
        if (!running) restart();
        else jump();
      }
    });

    restartBtn.addEventListener('click', () => {
      if (!running) restart();
      window.location.href = 'door/index.html';
    });
    restartBtn.addEventListener('touchstart', function() {
      if (!running) restart();
      window.location.href = 'door/index.html';
    });

    resetScoreBtn.addEventListener('click', () => {
      scores = [];
      localStorage.setItem('pixelGameScores', '[]');
      highScore = 0;
      avgScore = 0;
      updateScorePanel();
      window.location.href = 'tools/menu/index.html';
    });
    resetScoreBtn.addEventListener('touchstart', function() {
      scores = [];
      localStorage.setItem('pixelGameScores', '[]');
      highScore = 0;
      avgScore = 0;
      updateScorePanel();
      window.location.href = 'tools/menu/index.html';
    });

    function createObstacle() {
      // 计算当前最右侧障碍物的位置
      let maxRight = 0;
      for (let i = 0; i < obstacles.length; i++) {
        let left = parseInt(obstacles[i].style.left);
        if (left > maxRight) maxRight = left;
      }
      // 最小间距（像素）
      const minGap = 160 + Math.random() * 80;
      if (maxRight > 0 && maxRight > window.innerWidth - minGap) return;

      const obs = document.createElement('div');
      obs.className = 'obstacle';
      obs.style.left = window.innerWidth + 'px';
      game.appendChild(obs);
      obstacles.push(obs);
    }

    function restart() {
      if (!running) {
        scores.push(score);
        localStorage.setItem('pixelGameScores', JSON.stringify(scores));
        highScore = Math.max(...scores);
        avgScore = (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1);
      }
      score = 0;
      running = true;
      gameOverEl.style.display = 'none';
      obstacles.forEach(o => o.remove());
      obstacles = [];
      player.style.bottom = '48px';
      isJumping = false;
      velocity = 0;
      speed = 6;
      updateScorePanel();
      if (animationId) cancelAnimationFrame(animationId);
      loop();
    }

    function loop() {
      if (!running) return;
      // 跳跃物理
      let bottom = parseInt(player.style.bottom);
      if (isJumping) {
        bottom += velocity;
        velocity -= gravity;
        if (bottom <= 48) {
          bottom = 48;
          isJumping = false;
        }
        player.style.bottom = bottom + 'px';
      }
      // 障碍物移动
      for (let i = obstacles.length - 1; i >= 0; i--) {
        let obs = obstacles[i];
        let left = parseInt(obs.style.left);
        left -= speed;
        obs.style.left = left + 'px';
        // 碰撞检测
        if (
          left < 120 && left + 32 > 80 &&
          bottom < 88
        ) {
          running = false;
          gameOverEl.style.display = 'block';
          scores.push(score);
          localStorage.setItem('pixelGameScores', JSON.stringify(scores));
          highScore = Math.max(...scores);
          avgScore = (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1);
          updateScorePanel();
        }
        // 超出移除
        if (left < -32) {
          obs.remove();
          obstacles.splice(i, 1);
          score++;
          updateScorePanel();
          if (score % 5 === 0) speed += 0.7;
        }
      }
      // 随机生成障碍物，且保证间距
      if (Math.random() < 0.018 && obstacles.length < 3) createObstacle();
      if (running) animationId = requestAnimationFrame(loop);
    }

    // 自适应窗口
    function resizeGame() {
      document.querySelector('.game-container').style.width = window.innerWidth + 'px';
      document.querySelector('.ground').style.width = window.innerWidth + 'px';
    }
    window.addEventListener('resize', resizeGame);
    resizeGame();

    // 初始化
    updateScorePanel();
    restart();
  </script>
</body>
</html>