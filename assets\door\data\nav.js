let navData = [
            {
                "id": "1",
                "name": "我的地盘",
                "remark": "喜欢的网站",
                "parent_id": "0",
                "sort": 6,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "2",
                "name": "开发者",
                "remark": "程序员用的网站",
                "parent_id": "0",
                "sort": 5,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "3",
                "name": "快乐源泉",
                "remark": "影视小说",
                "parent_id": "0",
                "sort": 4,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "4",
                "name": "涨知识",
                "remark": "学习类相关",
                "parent_id": "0",
                "sort": 3,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "5",
                "name": "工具箱",
                "remark": "工具类网站",
                "parent_id": "0",
                "sort": 2,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "6",
                "name": "其他",
                "remark": "无法分类",
                "parent_id": "0",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "8",
                "name": "搜索引擎",
                "remark": "搜索引擎",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "10",
                "name": "未分类",
                "remark": "还没有详细分类的网站",
                "parent_id": "0",
                "sort": 0,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "11",
                "name": "我的网站",
                "remark": "我闲着无聊开发的网站",
                "parent_id": "1",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "12",
                "name": "编程开发",
                "remark": "我在敲代码",
                "parent_id": "1",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "13",
                "name": "发现有趣",
                "remark": "发现有趣的网站",
                "parent_id": "1",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "14",
                "name": "协同办公",
                "remark": "在线协同办公",
                "parent_id": "1",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "15",
                "name": "小伙伴们",
                "remark": "我的小伙伴们",
                "parent_id": "1",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "16",
                "name": "工具库",
                "remark": "字体、模板库",
                "parent_id": "2",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "17",
                "name": "教程网站",
                "remark": "教程网站",
                "parent_id": "2",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "18",
                "name": "设计灵感",
                "remark": "设计灵感",
                "parent_id": "2",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "19",
                "name": "博客论坛",
                "remark": "博客论坛",
                "parent_id": "2",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "20",
                "name": "前端UI",
                "remark": "前端UI库、工具库",
                "parent_id": "2",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "21",
                "name": "社交平台",
                "remark": "娱乐社交平台",
                "parent_id": "3",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "22",
                "name": "影视视频",
                "remark": "电影、电视剧、短视频",
                "parent_id": "3",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "23",
                "name": "漫画小说",
                "remark": "漫画小说",
                "parent_id": "3",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "24",
                "name": "游戏",
                "remark": "玩游戏",
                "parent_id": "3",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "25",
                "name": "其他",
                "remark": "其他小资源网站",
                "parent_id": "3",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "26",
                "name": "哲史文",
                "remark": "哲学、历史、文学",
                "parent_id": "4",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "27",
                "name": "社会交互",
                "remark": "社会性质的交互行为、活动",
                "parent_id": "4",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "28",
                "name": "其他",
                "remark": "奇怪的知识",
                "parent_id": "4",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "30",
                "name": "翻译网站",
                "remark": "在线翻译网站",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "31",
                "name": "软件下载",
                "remark": "软件下载",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "32",
                "name": "云盘",
                "remark": "云存储",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "33",
                "name": "地图",
                "remark": "地图",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "34",
                "name": "数码电器",
                "remark": "数码电器",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "35",
                "name": "修电脑",
                "remark": "修电脑",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "36",
                "name": "图片壁纸",
                "remark": "许多精美的图片壁纸",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "37",
                "name": "其他网站",
                "remark": "有兴趣的网站",
                "parent_id": "5",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "38",
                "name": "个人博客",
                "remark": "一些有意思的博主",
                "parent_id": "6",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "39",
                "name": "一些导航",
                "remark": "能发现有趣的东西",
                "parent_id": "6",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "93",
                "name": "互推伙伴",
                "remark": "互推伙伴",
                "parent_id": "1",
                "sort": 7,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "94",
                "name": "站长推荐",
                "remark": "站长推荐",
                "parent_id": "1",
                "sort": 6,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "95",
                "name": "站长网页",
                "remark": "站长网页",
                "parent_id": "1",
                "sort": 5,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "96",
                "name": "最近浏览",
                "remark": "最近浏览",
                "parent_id": "1",
                "sort": 4,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "97",
                "name": "我的收藏",
                "remark": "我的收藏",
                "parent_id": "1",
                "sort": 3,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "98",
                "name": "站内搜索",
                "remark": "站内搜索",
                "parent_id": "1",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "99",
                "name": "自定义路径",
                "remark": "自定义路径",
                "parent_id": "1",
                "sort": 2,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            },
            {
                "id": "100",
                "name": "未整理",
                "remark": "还没有整理的",
                "parent_id": "10",
                "sort": 1,
                "deleted": 0,
                "create_time": "2025-06-08T00:26:08.000+00:00",
                "update_time": "2025-06-08T00:26:08.000+00:00"
            }
        ]


