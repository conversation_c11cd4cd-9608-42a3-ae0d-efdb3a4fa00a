<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>SQL展示器</title>
  <style>
    body { font-family: 'Segoe UI', Arial, sans-serif; background: #f7f9fb; margin: 0; padding: 0; }
    .container { max-width: 900px; margin: 2rem auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 12px #0001; padding: 2rem; }
    .sql-input { width: 100%; min-height: 120px; font-size: 15px; border-radius: 6px; border: 1px solid #d0d7de; padding: 1em; margin-bottom: 1.2em; }
    .btn { background: #4f8cff; color: #fff; border: none; border-radius: 6px; padding: 8px 22px; font-size: 15px; cursor: pointer; margin-bottom: 1.5em; }
    .btn:hover { background: #357ae8; }
    .result-table { width: 100%; border-collapse: collapse; margin-top: 1.5em; }
    .result-table th, .result-table td { border: 1px solid #e3e8f7; padding: 8px 10px; text-align: left; }
    .result-table th { background: #f0f6ff; color: #357ae8; }
    .result-table tr:nth-child(even) { background: #f8fafd; }
    .highlight { color: #4f8cff; font-weight: bold; }
    .type { color: #27ae60; }
    .nullable { color: #e67e22; }
    .pk { color: #e74c3c; }
    .sql-tabs { margin-top: 2em; }
    .sql-tabs button { margin-right: 10px; padding: 6px 18px; border-radius: 6px; border: 1px solid #d0d7de; background: #f0f6ff; color: #357ae8; cursor: pointer; }
    .sql-tabs button.active { background: #4f8cff; color: #fff; border-color: #4f8cff; }
    .sql-output { background: #f8fafd; border-radius: 6px; padding: 1em; margin-top: 1em; font-family: 'Consolas', monospace; font-size: 15px; white-space: pre-wrap; }
    .error-tip { color: #e67e22; font-size: 14px; margin-bottom: 1em; }
  </style>
</head>
<body>
  <div class="container">
    <h2>SQL 创建语句分析展示器</h2>
    <textarea class="sql-input" id="sqlInput" placeholder="粘贴或输入SQL建表语句，支持MySQL/SQLServer/高斯/Oracle等"></textarea>
    <br>
    <button class="btn" onclick="analyzeSQL()">分析SQL</button>
    <div id="result"></div>
    <div class="sql-tabs" id="sqlTabs" style="display:none;">
      <button onclick="showSQL('mysql')" id="tab-mysql" class="active">MySQL</button>
      <button onclick="showSQL('oracle')" id="tab-oracle">Oracle</button>
      <button onclick="showSQL('sqlserver')" id="tab-sqlserver">SQLServer</button>
      <button onclick="showSQL('gauss')" id="tab-gauss">高斯</button>
    </div>
    <div class="sql-output" id="sqlOutput" style="display:none;"></div>
  </div>
  <script>
let sqlVariants = {};
let editableColumns = []; // 用于保存可编辑的列

function autoCorrectSQL(sql) {
  let corrected = sql.trim();
  if (!/;\s*$/.test(corrected)) corrected += ';';
  corrected = corrected.replace(/creat\s+table/i, 'CREATE TABLE');
  corrected = corrected.replace(/varchar2?/ig, 'VARCHAR');
  corrected = corrected.replace(/number/ig, 'NUMERIC');
  corrected = corrected.replace(/（/g, '(').replace(/）/g, ')');
  return corrected;
}

function analyzeSQL() {
  const sqlRaw = document.getElementById('sqlInput').value;
  const resultDiv = document.getElementById('result');
  const sqlTabs = document.getElementById('sqlTabs');
  const sqlOutput = document.getElementById('sqlOutput');
  sqlTabs.style.display = 'none';
  sqlOutput.style.display = 'none';
  sqlVariants = {};
  editableColumns = [];

  if (!sqlRaw.trim()) {
    resultDiv.innerHTML = '<p style="color:#e74c3c;">请输入SQL建表语句！</p>';
    return;
  }

  let sql = autoCorrectSQL(sqlRaw);
  let errorTip = '';
  if (sql !== sqlRaw) {
    errorTip = '<div class="error-tip">已自动纠正部分SQL格式或关键字，请检查。</div>';
  }

  const tableMatch = sql.match(/CREATE\s+TABLE\s+([`"\[]?)(\w+)\1\s*\(([\s\S]+?)\)[^)]*;/i)
    || sql.match(/CREATE\s+TABLE\s+([`"\[]?)(\w+)\1\s*\(([\s\S]+?)\)[^)]*$/i);
  if (!tableMatch) {
    resultDiv.innerHTML = errorTip + '<p style="color:#e74c3c;">未识别到有效的CREATE TABLE语句。</p>';
    return;
  }
  const tableName = tableMatch[2];
  const fieldsStr = tableMatch[3];
  const lines = fieldsStr.split(/,(?![^\(\[]*[\]\)])/).map(f => f.trim()).filter(f => f);
  const fieldLines = lines.filter(line => !/^(PRIMARY|UNIQUE|KEY|CONSTRAINT|INDEX|FOREIGN|CHECK)\b/i.test(line));
  let html = errorTip + `<h3>表名：<input id="editable-table-name" value="${tableName}" style="font-size:1.1em;width:auto;min-width:80px;max-width:200px;border:1px solid #d0d7de;border-radius:4px;padding:2px 6px;" /></h3>`;
  html += `<table class="result-table" id="editable-table"><tr><th>字段名</th><th>类型</th><th>约束</th></tr>`;
  let columns = [];
  fieldLines.forEach((field, idx) => {
    const m = field.match(/^([`"\[]?)(\w+)\1\s+([A-Z0-9_\(\), ]+)(.*)$/i);
    if (m) {
      const name = m[2];
      const type = m[3].trim();
      let constraint = '';
      if (/PRIMARY\s+KEY/i.test(field)) constraint += '<span class="pk">主键</span> ';
      if (/NOT\s+NULL/i.test(field)) constraint += '<span class="nullable">非空</span> ';
      if (/UNIQUE/i.test(field)) constraint += '唯一 ';
      if (/AUTO_INCREMENT|IDENTITY|SERIAL/i.test(field)) constraint += '自增 ';
      if (/DEFAULT\s+['"\w\d\(\)]+/i.test(field)) {
        const def = field.match(/DEFAULT\s+(['"\w\d\(\)]+)/i);
        if (def) constraint += `默认:${def[1]} `;
      }
      html += `<tr>
        <td><input class="editable-col-name" data-idx="${idx}" value="${name}" style="width:110px;max-width:180px;border:1px solid #d0d7de;border-radius:4px;padding:2px 6px;" /></td>
        <td><input class="editable-col-type" data-idx="${idx}" value="${type}" style="width:120px;max-width:200px;border:1px solid #d0d7de;border-radius:4px;padding:2px 6px;" /></td>
        <td>${constraint.trim()}</td>
      </tr>`;
      columns.push({
        name,
        type,
        rawType: type,
        isPK: /PRIMARY\s+KEY/i.test(field),
        isNotNull: /NOT\s+NULL/i.test(field),
        isUnique: /UNIQUE/i.test(field),
        isAuto: /AUTO_INCREMENT|IDENTITY|SERIAL/i.test(field),
        defaultVal: (field.match(/DEFAULT\s+(['"\w\d\(\)]+)/i) || [])[1] || null
      });
    }
  });
  html += `</table>`;
  const pkMatch = lines.find(line => /^PRIMARY\s+KEY/i.test(line));
  let pkFields = [];
  if (pkMatch) {
    const pkFieldMatch = pkMatch.match(/\(([^)]+)\)/);
    if (pkFieldMatch) {
      pkFields = pkFieldMatch[1].split(',').map(s => s.replace(/[`"\[\]\s]/g, ''));
      html += `<div style="margin-top:1em;"><span class="pk">表级主键：</span>${pkFields.join(', ')}</div>`;
    }
  }
  resultDiv.innerHTML = html;

  // 保存可编辑列
  editableColumns = columns;
  // 监听编辑
  bindEditableEvents();

  sqlVariants = generateSQLVariants(tableName, columns, pkFields);
  sqlTabs.style.display = 'block';
  showSQL('mysql');
}

function bindEditableEvents() {
  // 表名编辑
  const tableNameInput = document.getElementById('editable-table-name');
  tableNameInput.addEventListener('input', function() {
    updateSQLVariants();
  });
  // 字段名编辑
  document.querySelectorAll('.editable-col-name').forEach(input => {
    input.addEventListener('input', function() {
      const idx = +input.dataset.idx;
      editableColumns[idx].name = input.value;
      updateSQLVariants();
    });
  });
  // 字段类型编辑
  document.querySelectorAll('.editable-col-type').forEach(input => {
    input.addEventListener('input', function() {
      const idx = +input.dataset.idx;
      editableColumns[idx].type = input.value;
      editableColumns[idx].rawType = input.value;
      updateSQLVariants();
    });
  });
}

function updateSQLVariants() {
  const tableName = document.getElementById('editable-table-name').value;
  sqlVariants = generateSQLVariants(tableName, editableColumns, []);
  // 保持当前tab
  const activeTab = document.querySelector('.sql-tabs button.active');
  if (activeTab) {
    showSQL(activeTab.id.replace('tab-', ''));
  }
}

function showSQL(type) {
  document.getElementById('sqlOutput').style.display = 'block';
  ['mysql', 'oracle', 'sqlserver', 'gauss'].forEach(t => {
    document.getElementById('tab-' + t).classList.remove('active');
  });
  document.getElementById('tab-' + type).classList.add('active');
  document.getElementById('sqlOutput').textContent = sqlVariants[type] || '-- 暂无该类型SQL --';
}

function generateSQLVariants(table, columns, pkFields) {
  const typeMap = {
    mysql:    { 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INT', 'DATE': 'DATE', 'DATETIME': 'DATETIME', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'INT', 'BIGINT': 'BIGINT' },
    oracle:   { 'VARCHAR': 'VARCHAR2(255)', 'NUMERIC': 'NUMBER', 'DATE': 'DATE', 'DATETIME': 'DATE', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'NUMBER', 'BIGINT': 'NUMBER' },
    sqlserver:{ 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INT', 'DATE': 'DATE', 'DATETIME': 'DATETIME', 'TIMESTAMP': 'DATETIME', 'INT': 'INT', 'BIGINT': 'BIGINT' },
    gauss:    { 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INTEGER', 'DATE': 'DATE', 'DATETIME': 'TIMESTAMP', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'INTEGER', 'BIGINT': 'BIGINT' }
  };
  let result = {};
  ['mysql', 'oracle', 'sqlserver', 'gauss'].forEach(db => {
    let lines = [];
    columns.forEach(col => {
      let t = col.rawType.toUpperCase();
      Object.keys(typeMap[db]).forEach(k => {
        t = t.replace(new RegExp(k, 'g'), typeMap[db][k]);
      });
      let line = `  ${col.name} ${t}`;
      if (col.isAuto) {
        if (db === 'mysql') line += ' AUTO_INCREMENT';
        if (db === 'sqlserver') line += ' IDENTITY(1,1)';
      }
      if (col.isNotNull) line += ' NOT NULL';
      if (col.isUnique) line += ' UNIQUE';
      if (col.defaultVal) line += ` DEFAULT ${col.defaultVal}`;
      lines.push(line);
    });
    // 主键
    if (pkFields && pkFields.length) {
      lines.push(`  PRIMARY KEY (${pkFields.join(', ')})`);
    } else {
      columns.forEach(col => {
        if (col.isPK) lines.push(`  PRIMARY KEY (${col.name})`);
      });
    }
    result[db] = `CREATE TABLE ${table} (\n${lines.join(',\n')}\n);`;
  });
  return result;
}
  </script>
  <style>
/* 防止input把div/table撑开 */
.result-table input {
  box-sizing: border-box;
  width: 100%;
  min-width: 60px;
  max-width: 200px;
  font-size: 15px;
  padding: 2px 6px;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  background: #f8fafd;
}
#editable-table-name {
  min-width: 60px;
  max-width: 200px;
  width: auto;
  box-sizing: border-box;
}
  </style>
</body>
</html>