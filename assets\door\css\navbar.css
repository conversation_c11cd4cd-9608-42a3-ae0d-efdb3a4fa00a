
.nav-container{
    padding: 10px 0;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-header{
    width: 100%;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0s;
}

.nav-logo{
    height: 40px;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0s;

}


#navbar .nav-item{
    line-height: 40px;
    font-size: 14px;
    color: #3c3c3c;
    padding: 0 8px;
    cursor: pointer;
    font-weight: bold;
    white-space: nowrap;  /* 禁止换行 */
    text-overflow: ellipsis; /* 可选：溢出显示省略号 */
}
#navbar .nav-item:hover{
    color:#ff6969;
}

#navbar .nav-item.active{
    color:#38bcbd;
}

.nav-logo-img > span{
    line-height: 40px;
}

@media (max-width:575px){
    .nav-logo,.navbar {
        text-align: center;
        width: 100%;
    }
}
@media (min-width:576px){
    .nav-logo{
        padding: 0px 1rem;
        width: 200px;
    }
    .nav-header{
        display: grid;
        grid-template-columns: 200px 1fr;
    }
}















