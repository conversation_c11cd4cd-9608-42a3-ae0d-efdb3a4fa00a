
 function noRepeat(arr) {
  //定义一个新的临时数组 
  var newArr=[]; 
  //遍历当前数组 
  for(var i=0;i<arr.length;i++) {
    //如果当前数组的第i已经保存进了临时数组，那么跳过，
    //否则把当前项push到临时数组里面 
    if(newArr.indexOf(arr[i]) === -1) {  //indexOf() 判断数组中有没有字符串值，如果没有则返回 -1 
       newArr.push(arr[i]);
    }
    }
  return newArr
}

// 通用函数
function $(selector) {
  var method = selector.substr(0, 1) == '.' ? 'getElementsByClassName' : 'getElementById';
  return document[method](selector.substr(1));
}