<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/fan.png" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/index.css">
  <title>云风扇</title>
</head>
<body style="background-color: rgb(222, 236, 250);">
<div class="container">
  <div class="title"><img src="assets/image/title.png"></div>
  <div class="nav-right">当前温度：<span>25</span>°C</div>
  <div class="context">
    <div class="top"><span id="toast"></span></h3></div>
    <div class="fan-img"><img src="assets/image/fan.png" id="img"></div> 
    <div class="btn-group" id="btn">
      <button onclick="stop()" type="button" class="btn"><span>关</span></button>
      <button onclick="start(1)" type="button" class="btn"><span>1</span></button>
      <button onclick="start(2)" type="button" class="btn"><span>2</span></button>
      <button onclick="start(3)" type="button" class="btn"><span>3</span></button>
    </div>
  </div>
</div>
</body>
<script>
</script>
<style>

</style>
<script>
  document.body.style.height = document.documentElement.clientHeight + 'px'
  var fan = document.getElementById("img");
  var btn = document.getElementById("btn").children
  var currentSpeed = 0
  var ready=true
  function start(speed){
    if(!ready){return}
    if(currentSpeed==0){
      if(fan.className=="start-"+speed){ return;}
      ready=false;currentSpeed=speed;active(speed)
      fan.className = "start-"+speed
      setTimeout(function(){
        fan.className = "run-"+speed
        ready=true
      },2000)
    }else{
      currentSpeed=speed;active(speed)
      fan.className = "run-"+speed
    }
    
  }
  function stop(){
    if(!ready||currentSpeed==0){return}
    ready=false;active(0)
    fan.className = "run-"+currentSpeed+" stop-"+currentSpeed
    setTimeout(function(){fan.className = "stop-"+currentSpeed},1000)
    setTimeout(function(){
      fan.className = "";
      currentSpeed = 0;
      ready=true
    },2000)
  }

  function active(speed){
    for(i = 0,len=btn.length; i < len; i++) {
      if(i==speed){
        btn[i].className="btn active"
      }else{
        btn[i].className="btn"
      }
    }
  }

  // 
  // 创建 XMLHttpRequest 对象
var xhr = new XMLHttpRequest();
 
 // 设置 POST 请求的 URL
 xhr.open("POST", "http://www.webxml.com.cn/WebServices/WeatherWebService.asmx/getWeatherbyCityName"); // 将URL替换为目标API的地址
  
 // 设置请求头部信息（根据需要进行修改）
 xhr.setRequestHeader('Content-Type', 'application/json');
  
 // 定义请求完成后的处理函数
 xhr.onload = function() {
     if (xhr.status === 200) {
         console.log(xhr.responseText); // 输出服务器返回的结果
     } else {
         console.error("Error: " + xhr.status);
     }
 };
  
 // 构造请求体参数（JSON格式）
 var data = JSON.stringify({ theCityName: '广州' }); // 根据接口文档提供的字段名称和值进行修改
  
 // 发送请求
 xhr.send(data);


  /*
    animationDuration：动画执行一次所需时间
    animationTimingFunction：动画以何种运行轨迹完成一个周期
      （1）linear:表示动画从头到尾的速度都是相同的。
      （2）ease-in:表示动画以低速开始。
      （3）ease-out:表示动画以低速结束。
      （4）ease-in-out:表示动画以低速开始和结束。
    animationIterationCount：动画播放次数
      （1）直接写数字，自定义想要播放动画的次数。
      （2）infinite：设置动画无线循环播放。
  */
</script>
</html>