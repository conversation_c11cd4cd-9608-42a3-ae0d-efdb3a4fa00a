body{
  margin: 0;
  padding: 0;
  background-color: rgb(251, 238, 230);
}
.container{
  text-align: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
.row{width: 96%; max-width: 600px;}
.row>h3{ color: rgb(79, 54, 74);}
.result{
  position: relative;
  background-color: #FFF;
  padding: 10px;
  border-radius: 10px;
  margin-top: 1rem;
}
.result:not(:first-child) {
  border-top: 1px dashed #eee;
  margin-top: 2rem;
}
.result > h4{
  margin: 0;
  padding: 10px;
  color: rgb(79, 54, 74);
  border-bottom: 1px solid rgb(181, 150, 181);
  text-align: left;
}
.result > p{
  font-size: 14px;
  text-align: left;
  word-break:break-all;
  word-wrap: break-word;
}
.result > p > button{
  padding: 4px 6px;
  background-color: coral;
  margin: 6px;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  filter: hue-rotate(calc(var(--i)*60deg));
  border: none;
}
.result-btn{
  position: absolute;
  width: 60px;
  top: 12px;
  right: 10px;
  padding: 3px;
  border-radius: 3px;
  background-color: rgb(181, 150, 181);
  font-size: 14px;
  cursor: pointer;
  outline: none;
}
.result-btn:active {
  background: #ffb700;
  color: #fff;
}
.convert-box{
  position: relative;
}
.convert-text{
  width: 100%;
  height: 120px;
  font-size: 1rem;
  padding: 10px 80px 10px 10px;
  border-color: rgb(79, 54, 74);
  border-width: 5px;
  border-radius: 1rem;
  box-sizing: border-box;
}
.convert-btn{
  position: absolute;
  right: 0;
  height: 120px;
  width: 80px;
  border-width: 5px;
  border-radius:0 1rem 1rem 0;
  border-color: rgb(79, 54, 74);
  background-color: rgb(181, 150, 181);
  outline: none;
  cursor: pointer;
  font-weight: bold;
}
.hint>p{
  font-size: 8px;
  color: #999;
}
a,button,input,div,textarea{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  outline: none;
}
.history-item {
  background: #f7f3f7;
  border-radius: 6px;
  padding: 6px 10px;
  margin: 4px 0;
  cursor: pointer;
  font-size: 13px;
  text-align: left;
  transition: background 0.2s;
}
.history-item:hover {
  background: #e0d3e0;
}
#typeSelector {
  margin-bottom: 8px;
  text-align: left;
  font-size: 13px;
}