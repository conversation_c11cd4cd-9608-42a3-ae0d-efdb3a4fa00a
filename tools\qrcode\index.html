<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/zgyh.png" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/css/index.css">
  <title>常用二维码</title>
</head>
<body onClick="javascript :history.back(-1);">
  <div class="row" id="app"></div>
</body>

<script type="text/javascript" src="assets/data.js"></script>
<script type="text/javascript">
  /* 初始化数据 */
function init(){
  var contextStr = ""
  for(i = 0,len=data.length; i < len; i++) {
    contextStr += "<div class='col'><div class='box'>"
        + "<div><img src="+data[i].img+" alt="+data[i].title+" width='300px' height='300px'></div>"
        + "<div><span class='title'>"+data[i].title+"</span></div>"
        + "<div><span class='subtitle'>"+data[i].subtitle+"</span></div>"
        + "<div><span class='content'>"+data[i].content+"</span></div></div></div>"
  }
  var appElement = document.getElementById("app")
  appElement.innerHTML = contextStr
}

init()
/* 禁止右键 */
document.oncontextmenu = function () { return false };
</script>
</html>