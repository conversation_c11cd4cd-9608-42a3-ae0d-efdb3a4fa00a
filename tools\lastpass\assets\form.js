// 表单相关逻辑（原生JS实现）

(function () {
  // 表单元素
  const modal = document.getElementById('modal');
  const modalTitle = document.getElementById('modalTitle');
  const typeSelect = document.getElementById('type');
  const statusSelect = document.getElementById('status');
  const addressInput = document.getElementById('address');
  const usernameInput = document.getElementById('username');
  const passwordInput = document.getElementById('password');
  const secretInput = document.getElementById('secret');
  const remarkInput = document.getElementById('remark');
  const saveBtn = document.getElementById('saveBtn');
  const cancelBtn = document.getElementById('cancelBtn');
  const closeModalBtn = document.getElementById('closeModalBtn');
  const indexDiv = document.getElementById('index');
  const formContent = document.getElementById('formContent');

  // 当前编辑的数据
  let currentData = null;
  let onSave = null;

  // 类型切换时动态显示字段
  function updateFormFields() {
    const type = typeSelect.value;
    // 显示所有字段
    addressInput.parentElement.style.display = 'none';
    usernameInput.parentElement.style.display = 'none';
    passwordInput.parentElement.style.display = 'none';
    secretInput.parentElement.style.display = 'none';

    if (type === 'system' || type === 'link') {
      addressInput.parentElement.style.display = '';
    }
    if (type === 'system' || type === 'database' || type === 'n4a') {
      usernameInput.parentElement.style.display = '';
      passwordInput.parentElement.style.display = '';
    }
    if (type === 'n4a') {
      secretInput.parentElement.style.display = '';
    }
  }

  // 打开表单
  window.openPasswordForm = function (data, saveCallback) {
    currentData = data || {};
    onSave = saveCallback;
    modal.style.display = 'block';
    modalTitle.textContent = currentData.id ? '编辑密码本' : '添加新密码';
    indexDiv.textContent = currentData.id || '';
    typeSelect.value = currentData.type || 'system';
    statusSelect.value = currentData.status || '1';
    addressInput.value = currentData.address || '';
    usernameInput.value = currentData.username || '';
    passwordInput.value = currentData.password || '';
    secretInput.value = currentData.secret || '';
    remarkInput.value = currentData.remark || '';
    updateFormFields();

    // 密码显示/隐藏按钮绑定
    const toggleBtn = document.getElementById('togglePwd');
    const pwdInput = document.getElementById('password');
    if (toggleBtn && pwdInput) {
      toggleBtn.textContent = '👁️';
      toggleBtn.onclick = function() {
        if (pwdInput.type === 'password') {
          pwdInput.type = 'text';
          this.textContent = '🙈';
        } else {
          pwdInput.type = 'password';
          this.textContent = '👁️';
        }
      };
    }
  };

  // 关闭表单
  function closeForm() {
    modal.style.display = 'none';
    currentData = null;
    onSave = null;
  }

  // 保存表单
  function handleSave(e) {
    e.preventDefault();
    // 校验
    if (!typeSelect.value) {
      alert('请选择类型');
      return;
    }
    if (!statusSelect.value) {
      alert('请选择状态');
      return;
    }
    if ((typeSelect.value === 'system' || typeSelect.value === 'link') && !addressInput.value) {
      alert('请填写地址');
      return;
    }
    if ((typeSelect.value === 'system' || typeSelect.value === 'database' || typeSelect.value === 'n4a') && !usernameInput.value) {
      alert('请填写账号');
      return;
    }
    if ((typeSelect.value === 'system' || typeSelect.value === 'database' || typeSelect.value === 'n4a') && !passwordInput.value) {
      alert('请填写密码');
      return;
    }
    // 构造数据
    const result = {
      id: currentData.id || undefined,
      type: typeSelect.value,
      status: statusSelect.value,
      address: addressInput.value,
      username: usernameInput.value,
      password: passwordInput.value,
      secret: secretInput.value,
      remark: remarkInput.value
    };
    if (typeof onSave === 'function') {
      onSave(result);
    }
    closeForm();
  }

  // 事件绑定
  typeSelect.addEventListener('change', updateFormFields);
  saveBtn.addEventListener('click', handleSave);
  cancelBtn.addEventListener('click', closeForm);
  closeModalBtn.addEventListener('click', closeForm);

  // ESC关闭
  window.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') closeForm();
  });

  // 初始化字段显示
  updateFormFields();
})();