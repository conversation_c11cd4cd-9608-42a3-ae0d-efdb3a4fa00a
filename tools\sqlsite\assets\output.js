
// 表格生成SQL
function generateSQLFromTable() {
  const resultDiv = document.getElementById('result');
  let tableName = editableTableName;
  let columns = editableColumns;
  // 若当前页面有表格，取表单内容
  const tableNameInput = document.getElementById('editable-table-name');
  if (tableNameInput) tableName = tableNameInput.value;
  const colInputs = document.querySelectorAll('.editable-col-name');
  const typeInputs = document.querySelectorAll('.editable-col-type');
  const constraintInputs = document.querySelectorAll('.editable-col-constraint');
  columns = [];
  for (let i = 0; i < colInputs.length; i++) {
    columns.push({
      name: colInputs[i].value,
      type: typeInputs[i].value,
      rawType: typeInputs[i].value,
      constraint: constraintInputs[i].value
    });
  }
  editableColumns = columns;
  editableTableName = tableName;
  // 渲染表格
  let html = `<h3>表名：<input id="editable-table-name" value="${tableName}" /></h3>`;
  html += `<table class="result-table" id="editable-table"><tr><th>字段名</th><th>类型</th><th>约束</th><th>操作</th></tr>`;
  columns.forEach((col, idx) => {
    html += `<tr>
      <td><input class="editable-col-name" data-idx="${idx}" value="${col.name}" /></td>
      <td>
        <select class="editable-col-type" data-idx="${idx}">
          ${typeOptions.map(opt => `<option value="${opt}" ${col.type.toUpperCase().startsWith(opt) ? 'selected' : ''}>${opt}</option>`).join('')}
        </select>
      </td>
      <td>
        <select class="editable-col-constraint" data-idx="${idx}">
          ${constraintOptions.map(opt => `<option value="${opt.value}" ${col.constraint === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
        </select>
      </td>
      <td><button class="del-col-btn" data-idx="${idx}" title="删除">删除</button></td>
    </tr>`;
  });
  html += `</table><button class="add-col-btn" onclick="addColumn()">+ 添加字段</button>`;
  resultDiv.innerHTML = html;
  bindEditableEvents();
  sqlVariants = generateSQLVariants(editableTableName, editableColumns);
  document.getElementById('sqlTabs').style.display = 'block';
  showSQL('mysql');
}

function addColumn() {
  editableColumns.push({ name: '', type: 'VARCHAR', rawType: 'VARCHAR', constraint: '' });
  generateSQLFromTable();
}

function bindEditableEvents() {
  // 表名编辑
  const tableNameInput = document.getElementById('editable-table-name');
  if (tableNameInput) {
    tableNameInput.addEventListener('input', function() {
      editableTableName = tableNameInput.value;
      sqlVariants = generateSQLVariants(editableTableName, editableColumns);
      showSQL(getCurrentTab());
    });
  }
  // 字段名编辑
  document.querySelectorAll('.editable-col-name').forEach(input => {
    input.addEventListener('input', function() {
      const idx = +input.dataset.idx;
      editableColumns[idx].name = input.value;
      sqlVariants = generateSQLVariants(editableTableName, editableColumns);
      showSQL(getCurrentTab());
    });
  });
  // 字段类型编辑
  document.querySelectorAll('.editable-col-type').forEach(select => {
    select.addEventListener('change', function() {
      const idx = +select.dataset.idx;
      editableColumns[idx].type = select.value;
      editableColumns[idx].rawType = select.value;
      sqlVariants = generateSQLVariants(editableTableName, editableColumns);
      showSQL(getCurrentTab());
    });
  });
  // 字段约束编辑
  document.querySelectorAll('.editable-col-constraint').forEach(select => {
    select.addEventListener('change', function() {
      const idx = +select.dataset.idx;
      editableColumns[idx].constraint = select.value;
      sqlVariants = generateSQLVariants(editableTableName, editableColumns);
      showSQL(getCurrentTab());
    });
  });
  // 删除字段
  document.querySelectorAll('.del-col-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const idx = +btn.dataset.idx;
      editableColumns.splice(idx, 1);
      generateSQLFromTable();
    });
  });
}

function getCurrentTab() {
  const activeTab = document.querySelector('.sql-tabs button.active');
  return activeTab ? activeTab.id.replace('tab-', '') : 'mysql';
}

function showSQL(type) {
  document.getElementById('sqlOutput').style.display = 'block';
  document.getElementById('sqlTabs').style.display = 'block';
  ['mysql', 'oracle', 'sqlserver', 'gauss'].forEach(t => {
    document.getElementById('tab-' + t).classList.remove('active');
  });
  document.getElementById('tab-' + type).classList.add('active');
  document.getElementById('sqlOutput').textContent = sqlVariants[type] || '-- 暂无该类型SQL --';
}

function generateSQLVariants(table, columns) {
  const typeMap = {
    mysql:    { 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INT', 'DATE': 'DATE', 'DATETIME': 'DATETIME', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'INT', 'BIGINT': 'BIGINT', 'TEXT': 'TEXT' },
    oracle:   { 'VARCHAR': 'VARCHAR2(255)', 'NUMERIC': 'NUMBER', 'DATE': 'DATE', 'DATETIME': 'DATE', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'NUMBER', 'BIGINT': 'NUMBER', 'TEXT': 'CLOB' },
    sqlserver:{ 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INT', 'DATE': 'DATE', 'DATETIME': 'DATETIME', 'TIMESTAMP': 'DATETIME', 'INT': 'INT', 'BIGINT': 'BIGINT', 'TEXT': 'TEXT' },
    gauss:    { 'VARCHAR': 'VARCHAR(255)', 'NUMERIC': 'INTEGER', 'DATE': 'DATE', 'DATETIME': 'TIMESTAMP', 'TIMESTAMP': 'TIMESTAMP', 'INT': 'INTEGER', 'BIGINT': 'BIGINT', 'TEXT': 'TEXT' }
  };
  let result = {};
  ['mysql', 'oracle', 'sqlserver', 'gauss'].forEach(db => {
    let lines = [];
    columns.forEach(col => {
      if (!col.name) return;
      let t = col.rawType.toUpperCase();
      Object.keys(typeMap[db]).forEach(k => {
        t = t.replace(new RegExp(k, 'g'), typeMap[db][k]);
      });
      let line = `  ${col.name} ${t}`;
      if (col.constraint === 'PRIMARY KEY') {
        // 主键单独处理
      } else {
        if (col.constraint === 'NOT NULL') line += ' NOT NULL';
        if (col.constraint === 'UNIQUE') line += ' UNIQUE';
        if (col.constraint === 'AUTO_INCREMENT') {
          if (db === 'mysql') line += ' AUTO_INCREMENT';
          if (db === 'sqlserver') line += ' IDENTITY(1,1)';
        }
      }
      lines.push(line);
    });
    // 主键
    let pkCols = columns.filter(col => col.constraint === 'PRIMARY KEY').map(col => col.name);
    if (pkCols.length) {
      lines.push(`  PRIMARY KEY (${pkCols.join(', ')})`);
    }
    result[db] = `CREATE TABLE ${table} (\n${lines.join(',\n')}\n);`;
  });
  return result;
}