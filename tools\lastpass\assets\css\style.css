

/* 主容器布局 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部区域样式 */
.header {
  padding: 1rem;
  background-color: var(--card-background);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

/* 应用标题 */
.app-title {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 搜索区域 */
.search-section {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.search-container {
  display: flex;
  gap: 12px;
  background: white;
  padding: 8px;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  align-items: center;
}

.search-container input {
  flex: 1;
  padding: 0.6rem 1rem;
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
  min-width: 200px;
  box-sizing: border-box;
}

.search-container select {
  width: 120px;
  padding: 0.6rem 1rem;
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
  cursor: pointer;
}

/* 操作按钮组样式 */
.search-container .add-button,
.search-container .import-export label,
.search-container .import-export button {
    padding: 0.6rem 1rem;
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
    white-space: nowrap;
    height: 42px;
    line-height: 1;
    box-sizing: border-box;
}

.search-container .add-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.search-container .add-button:hover {
  background-color: #357abd;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.25);
}


.search-container .import-export {
  display: flex;
  gap: 8px;
  margin: 0;
}

.search-container .import-export input[type="file"] {
  display: none;
}

.search-container .import-export label,
.search-container .import-export button {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  cursor: pointer;
  margin: 0;
  min-width: unset;
}

.search-container .import-export label:hover,
.search-container .import-export button:hover {
  background-color: rgba(74, 144, 226, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}


/* 响应式调整 */
@media (max-width: 768px) {
  .search-container {
      flex-direction: column;
      padding: 12px;
  }
  
  .search-container input,
  .search-container select,
  .search-container .add-button,
  .search-container .import-export {
      width: 100%;
  }
  
  .search-container .import-export {
      display: grid;
      grid-template-columns: 1fr 1fr;
  }
  
  .search-container .import-export label,
  .search-container .import-export button {
      width: 100%;
      text-align: center;
  }
}

/* 导入导出区域 */
.import-export {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.import-export input[type="file"] {
  display: none;
}

.import-export label,
.import-export button {
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
  min-width: 100px;
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.import-export label:hover,
.import-export button:hover {
  background-color: rgba(74, 144, 226, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header {
      padding: 24px var(--spacing) 20px;
      gap: 20px;
  }
  
  .app-title {
      font-size: 24px;
  }
  
  .search-container {
      flex-direction: column;
      padding: 12px;
  }
  
  .search-container input,
  .search-container select,
  .search-container .add-button {
      width: 100%;
  }
  
  .import-export {
      flex-direction: column;
      width: 100%;
      padding: 0 12px;
  }
  
  .import-export label,
  .import-export button {
      width: 100%;
  }
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 0 var(--spacing) var(--spacing);
  width: 96%;
  margin: auto;
  box-sizing: border-box;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header {
      padding: 32px var(--spacing) 24px;
      gap: 24px;
  }
  
  .app-title {
      font-size: 28px;
  }
  
  .search-container {
      flex-direction: column;
      padding: 12px;
  }
  
  .search-container input,
  .search-container select {
      width: 100%;
      min-width: unset;
  }
  
  .import-export {
      flex-direction: column;
      width: 100%;
  }
  
  .import-export label,
  .import-export button,
  .add-button {
      width: 100%;
  }
  
  .password-list {
      margin-top: 24px;
  }
}

/* 密码卡片列表容器 */
.password-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-width: var(--container-width);
  margin: 24px auto;
  padding: 0 var(--spacing);
}

/* 密码卡片样式 */
.password-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  overflow: hidden;
  color: var(--text-color);
}

.password-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.12);
}

.password-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.2;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.card-type {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 16px;
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-normal { 
  background-color: rgba(46, 204, 113, 0.1); 
  color: var(--success-color);
}

.status-invalid { 
  background-color: rgba(241, 196, 15, 0.1); 
  color: var(--warning-color);
}

.status-deleted { 
  background-color: rgba(231, 76, 60, 0.1); 
  color: var(--danger-color);
}

.status-wrong { 
  background-color: rgba(231, 76, 60, 0.1); 
  color: var(--danger-color);
}

/* 字段样式 */
.field {
  margin-bottom: 12px;
  position: relative;
}

.field:last-child {
  margin-bottom: 0;
}

.field label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-value {
  flex: 1;
  background-color: #f8f9fa;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  word-break: break-all;
  line-height: 1.4;
}

/* TOTP动态口令样式 */
.totp-container {
  background: linear-gradient(to right, #f8f9fa, #f1f3f5);
  border-radius: var(--border-radius);
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.totp-code {
  font-family: 'Roboto Mono', monospace;
  font-size: 28px;
  font-weight: 600;
  color: var(--primary-color);
  letter-spacing: 4px;
  text-align: center;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.totp-timer {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.totp-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(0,0,0,0.1);
  border-radius: 2px;
  overflow: hidden;
}

.totp-progress-bar {
  height: 100%;
  background: linear-gradient(to right, var(--primary-color), #357abd);
  transition: width 1s linear;
}

/* 卡片操作按钮 */
.card-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.card-actions button {
  padding: 8px 16px;
  font-size: 13px;
  min-width: 80px;
}

/* 复制按钮样式 */
.copy-button {
  padding: 4px 12px;
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  font-size: 12px;
  white-space: nowrap;
  min-width: unset;
  opacity: 0;
  transition: var(--transition);
  height: fit-content;
  border-radius: 4px;
}

.field:hover .copy-button {
  opacity: 1;
}

.copy-button:hover {
  background-color: var(--primary-color);
  color: white;
  box-shadow: none;
  transform: none;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .password-list {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  :root {
      --header-height: auto;
  }

  .search-bar {
      position: sticky;
      padding: 12px var(--spacing);
  }
  
  .search-section,
  .action-section {
      flex-direction: column;
  }
  
  .search-bar input,
  .search-bar select,
  .import-export {
      width: 100%;
  }
  
  .password-list {
      grid-template-columns: 1fr;
      gap: 16px;
      margin: 16px auto;
  }
  
  .main-content {
      margin-top: 0;
      padding: 12px;
  }
  
  .modal-content {
      width: 90%;
      margin: 20px;
      padding: 16px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
      --background-color: #1a1a1a;
      --card-background: #2d2d2d;
      --text-color: #ffffff;
  }
  
  .field-value {
      background-color: #363636;
  }
  
  .totp-container {
      background: linear-gradient(to right, #363636, #404040);
  }
  
  .card-header,
  .card-actions {
      border-color: #404040;
  }
  
  .toast {
      background-color: #363636;
      border-color: #404040;
  }
  
  .modal-content::-webkit-scrollbar-thumb {
      background-color: rgba(255,255,255,0.2);
      border: 2px solid var(--card-background);
  }
  
  .modal-content::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255,255,255,0.3);
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  margin: 24px auto;
  max-width: var(--container-width);
  padding: 0 var(--spacing);
}

.empty-state-content {
  text-align: center;
  color: #666;
}

.empty-state-content h3 {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--text-color);
}

.empty-state-content p {
  font-size: 14px;
  margin: 0;
}

/* 按钮基础样式 */
button {
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-radius);
  background-color: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  font-size: 14px;
}

button:hover {
  background-color: #357abd;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

button.danger {
  background-color: var(--danger-color);
}

button.danger:hover {
  background-color: #c0392b;
}

/* 表单样式 */
.password-form h2 {
  margin: 0 0 24px 0;
  color: var(--text-color);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  cursor: pointer;
  display: none;
}

.modal-content {
  background-color: var(--card-background);
  padding: 24px;
  margin: 7vh auto 0 auto;
  border-radius: var(--border-radius-lg);
  width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 16px 32px rgba(0,0,0,0.15);
  position: relative;
  cursor: default;
  display: flex;
  flex-direction: column;
}

.modal-form-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
  margin: 0 -24px;

}

/* 自定义滚动条 */
.modal-form-container::-webkit-scrollbar {
  width: 6px;
}

.modal-form-container::-webkit-scrollbar-track {
  background: transparent;
  margin: 24px 0;
}

.modal-form-container::-webkit-scrollbar-thumb {
  background-color: rgba(0,0,0,0.2);
  border-radius: 3px;
}

.modal-form-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0,0,0,0.3);
}

/* 表单样式 */
.password-form {
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

.password-form h2 {
  position: sticky;
  top: 0;
  background-color: var(--card-background);
  margin: 0 -24px 24px -24px;
  padding: 24px 24px 12px;
  border-bottom: 1px solid #eee;
  z-index: 1;
}

/* 表单底部操作按钮 */
.form-actions {
  position: sticky;
  bottom: 0;
  background-color: var(--card-background);
  margin: 24px -24px 0;
  padding: 12px 24px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  z-index: 1;
  flex-shrink: 0;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .password-form h2 {
      border-bottom-color: #404040;
  }
  
  .form-actions {
      border-top-color: #404040;
  }
  
  .modal-form-container::-webkit-scrollbar-thumb {
      background-color: rgba(255,255,255,0.2);
  }
  
  .modal-form-container::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255,255,255,0.3);
  }
}

/* 提示框样式 */
.toast-container {
  position: fixed;
  top: var(--header-height);
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  pointer-events: none;
}

.toast {
  background-color: var(--card-background);
  color: var(--text-color);
  padding: 12px 24px;
  border-radius: var(--border-radius);
  margin-top: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  animation: slideDown 0.3s ease, fadeOut 0.3s ease 2.7s;
  border: 1px solid #eee;
  pointer-events: auto;
}

@keyframes slideDown {
  from {
      transform: translateY(-100%);
      opacity: 0;
  }
  to {
      transform: translateY(0);
      opacity: 1;
  }
}

@keyframes fadeOut {
  from {
      transform: translateX(0);
      opacity: 1;
  }
  to {
      transform: translateX(100%);
      opacity: 0;
  }
}

/* 导入导出按钮容器 */
.import-export {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.import-export input[type="file"] {
  display: none;
}

.import-export label {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.import-export label:hover {
  background-color: #357abd;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 数据库链接特殊样式 */
.connection-string {
  white-space: pre-wrap;
  max-height: 80px;
  overflow-y: auto;
}

/* TOTP容器特殊样式 */
.totp-container .copy-button {
  position: absolute;
  top: 12px;
  right: 12px;
}

/* 关闭按钮样式 */
.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: var(--transition);
  min-width: unset;
  z-index: 2;
}

.modal-close:hover {
  color: var(--danger-color);
  background-color: rgba(0,0,0,0.05);
  transform: none;
  box-shadow: none;
}

.modal-close::before,
.modal-close::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 2px;
  background-color: currentColor;
  transform-origin: center;
}

.modal-close::before {
  transform: rotate(45deg);
}

.modal-close::after {
  transform: rotate(-45deg);
}

/* 表单内容容器 */
.modal-form-container {
  margin-top: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .modal-content {
      width: calc(100% - 32px);
      margin: 16px;
      padding: 20px;
  }
  
  .modal-form-container {
      padding: 0 20px;
      margin: 0 -20px;
  }
  
  .password-form h2 {
      margin: 0 -20px 20px -20px;
      padding: 20px 20px 12px;
  }
  
  .form-actions {
      margin: 20px -20px 0;
      padding: 12px 20px;
  }
  
  .modal-close {
      top: 16px;
      right: 16px;
  }
} 
/* Modern Card & Button Enhancements - uiverse.io 风格增强 */

/* 卡片阴影与圆角 */
.password-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08), 0 1.5px 4px 0 rgba(0,0,0,0.03);
  padding: 20px 24px;
  margin-bottom: 24px;
  transition: box-shadow 0.2s, transform 0.2s;
  border: none;
  position: relative;
  overflow: hidden;
}
.password-card:hover {
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.16), 0 3px 8px 0 rgba(0,0,0,0.06);
  transform: translateY(-2px) scale(1.01);
}

/* 卡片顶部彩色标签 */
.password-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.password-card .card-type {
  font-weight: bold;
  color: #4f8cff;
  background: #eaf3ff;
  border-radius: 8px;
  padding: 2px 10px;
  font-size: 13px;
}
.status-badge {
  border-radius: 8px;
  padding: 2px 10px;
  font-size: 13px;
  color: #fff;
}
.status-1 { background: #4caf50; }
.status-2 { background: #ff9800; }
.status-3 { background: #f44336; }
.status-4 { background: #bdbdbd; }

/* 卡片内容排版 */
.card-fields .field {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.card-fields .field label {
  min-width: 56px;
  color: #888;
  font-size: 13px;
  margin-right: 8px;
}
.card-fields .field-value {
  font-size: 15px;
  color: #222;
  word-break: break-all;
}

/* 卡片按钮动效 */
.password-card .card-actions button {
  border: none;
  background: #f5f6fa;
  color: #4f8cff;
  border-radius: 8px;
  padding: 6px 16px;
  margin-right: 8px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.password-card .card-actions button:hover {
  background: #4f8cff;
  color: #fff;
}
.password-card .card-actions .danger {
  color: #f44336;
}
.password-card .card-actions .danger:hover {
  background: #f44336;
  color: #fff;
}

/* 复制按钮动画 */
.copy-btn {
  border: none;
  background: #eaf3ff;
  color: #4f8cff;
  border-radius: 6px;
  padding: 2px 10px;
  margin-left: 8px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  font-size: 13px;
}
.copy-btn.copied {
  background: #4f8cff;
  color: #fff;
}

/* Toast 动画优化 */
.toast {
  background: #4f8cff;
  color: #fff;
  border-radius: 8px;
  padding: 10px 24px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(79,140,255,0.15);
  animation: fadeInOut 1.5s;
}
@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(10px);}
  10% { opacity: 1; transform: translateY(0);}
  90% { opacity: 1; }
  100% { opacity: 0; transform: translateY(-10px);}
}

/* 表单弹窗圆角与阴影 */
.modal-content {
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.16), 0 3px 8px 0 rgba(0,0,0,0.06);
  background: #fff;
  padding: 32px 24px;
}
