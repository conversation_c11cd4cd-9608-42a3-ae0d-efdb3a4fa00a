
.aside{
  transition:all .5s .2s linear;
}

@media screen and (min-width: 600px) {
  .aside{
    position: fixed;
    left: 0px;
    width: 200px;
    height: 100%;
    float: left;
    background-color: #4A4E59;
    color: white;
    text-align: center;
    z-index:999;
  }

  .aside-title,.aside-name{
    font-size: 1em;
    font-weight: bolder;
    line-height: 1.5em;
  }

  .aside-name{
    font-size: 2em;
    margin-top: 50%;
  }

  .aside-tag{
    margin-top: 10%;
  }

  .aside-tag-title{
    width: 100%;
    background-color: #2192BC;
    margin: 0px 0 10px 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    font-weight: bolder;
    font-size: 1em;
    line-height: 1.5em;
  }

  .aside-tag-targon{
    width: 0px;
    height: 0px;
    border-color: #114F65 transparent;
    border-width: 0px 20px 10px 0px;
    border-style: solid;
    margin-left: 200px;
  }

  .aside-info{
    text-align: left;
    font-size: 0.8em;
    margin-left: 1em;
    text-indent: 1em;
  }
  .aside-info>p{
    margin-bottom: 3px;
  }
}

@media screen and (max-width: 600px) {
  .aside{
    width: 100%;
    background-color: #4A4E59;
    color: white;
    transition:all .5s .2s linear;
  }

  .aside-title{
    text-align: right;
    padding-right: 1rem;
    line-height: 2rem;
    float: right;
  }

  .aside-name{
    font-size: 2em;
    font-weight: bolder;
    padding-left: 1rem;
  }
  .aside-tag{
    margin: 10px 0;
  }
  .aside-tag-title{
    background-color: #2192BC;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    font-weight: bolder;
    font-size: 1em;
    line-height: 2em;
    text-indent: 1rem;
  }

  .aside-info{
    width: 90%;
    margin: auto;
    clear:both
  }
  /* float后占位 */
  .aside-info:after {
    content: '\20';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
    overflow: hidden;
  }

  .aside-info>p{
    flex-basis: 0;
    flex-grow: 2;
    width: 50%;
    float: left;
    margin-bottom: 6px;
  }
}


