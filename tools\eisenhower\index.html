<template>

    <button @click="showTaskOper(null)">添加任务</button>
    <div class="eisenhower-matrix">
      <template v-for="quadrant in quadrants" :key="quadrant.type">
        <div class="quadrant" >
          <div class="title">
            <p id="title-content">{{ quadrant.title }}</p>
            
            <button >查看所有</button>
          </div>
          <ul>
            <li v-for="task in getTasksByType(quadrant.type).slice(0, 10)"
                :key="task.id"
                class="accordion-item"
                :class="{ 'active': activeIndex === task.id }"
                @click="toggle(task.id)"
            >
              <div class="accordion-header">
                <div class="accordion-title">{{ task.title }}</div>
                <div class="accordion-right">
                  <button @click="showTaskOper(task)">编辑</button>
                  <button @click="deleteTask(task.id)">删除</button>
                  <button @click="toggleTop(task.id)">置顶</button>
                </div>
              </div>
              <div class="accordion-content">
                {{ task.title }}
              </div>
            </li>
          </ul>
        </div>
      </template>
      <div v-if="showModal" class="modal">
        <div class="modal-content">
          <span class="close" @click="closeModal">&times;</span>
          <div class="card">
          <span class="title">新增计划</span>
          {{task}}
          <modal-header class="modal-header">
            <div class="modal-group">
              <input placeholder="‎" type="text" v-model="task.title" required="">
              <label for="title">计划标题</label>
            </div>
            <div class="modal-group">
                <textarea placeholder="‎" id="content" v-model="task.content" name="content" rows="5" ></textarea>
                <label for="content">内容详情</label>
            </div>
            <button type="submit" @click="addEditTask(task)">Submit</button>
          </modal-header>
        </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  
  interface Task {
    id: number
    title: string
    content: string
    deleted: boolean
    createTime: number
    finishTime: number | null
    type: string
    seqnum: number
    isTop: boolean
  }
  const task = reactive<Task>({
    id: 0,
    title: '',
    content: '',
    deleted: false,
    createTime: Date.now(),
    finishTime: null,
    type: 'urgentImportant',
    seqnum: 0,
    isTop: false
  })
  const tasks = reactive<Task[]>([])
  const showModal = ref(false)
  const modalTasks = ref<Task[]>([])
  const modalTitle = ref('')
  const currentPage = ref(1)
  const itemsPerPage = 10
  
  const quadrants = [
    { type: 'urgentImportant', title: '重要且紧急' },
    { type: 'importantNotUrgent', title: '重要但不紧急' },
    { type: 'urgentNotImportant', title: '紧急但不重要' },
    { type: 'notUrgentNotImportant', title: '不紧急且不重要' }
  ]
  
  const getTasksByType = (type: string) => {
    return tasks
      .filter(task => task.type === type && !task.deleted)
      .sort((a, b) => {
        if (a.isTop !== b.isTop) {
          return a.isTop ? -1 : 1;
        }
        return b.seqnum - a.seqnum;
      })
  }
  
  const showTaskOper = (t:Task | null) => {
    console.log(t);
    if (task.id === null) {
      task.title = ''
      task.content = ''
      task.type = 'urgentImportant'
    } else {
      // 由于 task 是响应式对象，不能直接赋值，改为逐个属性更新
      Object.assign(task, t);
    }
    // modalTasks.value = getTasksByType(t.type)
    // modalTitle.value = quadrants.find(q => q.type === t.type)?.title || ''
    showModal.value = true
  }
  const addEditTask = () => {
    if (task.id == undefined) {
      // 新增
      task.id = tasks.length + 1;
      task.seqnum = tasks.length + 1;
      tasks.push(task)
    } else {
      // 编辑
      let index = tasks.findIndex(t => t.id === task.id);
      if (index !== -1) {
        // 替换对象
        tasks.splice(index, 1, task);
      }
    }
    saveTasks()
  }
  
  const deleteTask = (id: number) => {
    const index = tasks.findIndex(task => task.id === id)
    if (index !== -1) {
      tasks[index].deleted = true
      saveTasks()
    }
  }
  const activeIndex = ref<number | null>(null)
  
  const toggle = (index: number) => {
    activeIndex.value = activeIndex.value === index ? null : index
  }
  const toggleTop = (id: number) => {
    const index = tasks.findIndex(task => task.id === id)
    if (index !== -1) {
      tasks[index].isTop = !tasks[index].isTop
      saveTasks()
    }
  }
  
  
  const closeModal = () => {
    showModal.value = false
  }
  
  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--
    }
  }
  
  const nextPage = () => {
    if (currentPage.value * itemsPerPage < modalTasks.value.length) {
      currentPage.value++
    }
  }
  
  const saveTasks = () => {
    localStorage.setItem('tasks', JSON.stringify(tasks))
  }
  
  const loadTasks = () => {
    const tasksData = localStorage.getItem('tasks')
    if (tasksData) {
      tasks.splice(0, tasks.length, ...JSON.parse(tasksData))
    }
  }
  
  onMounted(() => {
    loadTasks()
  })
  </script>
  
  <style scoped>
  .eisenhower-matrix {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 20px;
  }
  
  .quadrant {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
  }
  .modal {
    display: block;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  .modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
  }
  
  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }
  
  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
  
  .accordion-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    overflow: hidden;
  }
  
  .accordion-header {
    padding: 15px;
    background-color: #f5f5f5;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .accordion-content {
    max-height: 0;
    padding: 0 15px;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
  }
  
  .accordion-item.active .accordion-content {
    max-height: 200px;
    padding: 15px;
  }
  
  .card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    width: 350px;
    display: flex;
    flex-direction: column;
    margin: auto;
  }
  
  .title {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
  }
  
  .modal-header {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
  }
  
  .modal-group {
    position: relative;
  }
  
  .modal-header .modal-group label {
    font-size: 14px;
    color: rgb(99, 102, 102);
    position: absolute;
    top: -10px;
    left: 10px;
    background-color: #fff;
    transition: all .3s ease;
  }
  
  .modal-header .modal-group input,
  .modal-header .modal-group textarea {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
    outline: 0;
    width: 100%;
    background-color: transparent;
  }
  
  .modal-header .modal-group input:placeholder-shown+ label, .modal-header .modal-group textarea:placeholder-shown +label {
    top: 10px;
    background-color: transparent;
  }
  
  .modal-header .modal-group input:focus,
  .modal-header .modal-group textarea:focus {
    border-color: #3366cc;
  }
  
  .modal-header .modal-group input:focus+ label, .modal-header .modal-group textarea:focus +label {
    top: -10px;
    left: 10px;
    background-color: #fff;
    color: #3366cc;
    font-weight: 600;
    font-size: 14px;
  }
  
  .modal-header .modal-group textarea {
    resize: none;
    height: 100px;
  }
  
  .modal-header button {
    background-color: #3366cc;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 10px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .modal-header button:hover {
    background-color: #27408b;
  }
  
  
  
  /* 可视窗口小于768px */
  @media screen and (max-width: 768px) {
    .eisenhower-matrix {
      grid-template-columns: 1fr;
    }
  }
  </style>