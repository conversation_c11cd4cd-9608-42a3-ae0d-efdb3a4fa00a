
.menu-container {
    margin-top: 1rem;
    display: grid;
    grid-template-columns: auto;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.menu-warp{
    margin: 0 0.6rem;
}

/* 空状态样式 */
.menu-empty-state {
    text-align: center;
    color: #666;
    width: 100%;
    padding: 3rem;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .menu-empty-icon {
    font-size: 48px;
    margin-bottom: 15px;
  }

  .menu-empty-state h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #333;
  }

  .menu-empty-state p {
    font-size: 16px;
    color: #888;
  }


.menu-content {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    width: 100%;
    margin-bottom: 1rem;
}

.menu-content-header {
    padding: 13px 16px;
    border-bottom: 1px solid #f1f4f9;
    line-height: 16px;
    width: 100%;
}

.menu-content-body{
    padding:12px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
}

@media (max-width:319px) {
    .menu-content-body{
        grid-template-columns: 1fr;
    }
}
@media (min-width:320px) {
    .menu-content-body{
        grid-template-columns: 1fr 1fr;
    }
}
@media (min-width:576px) {
    .menu-content-body{
        grid-template-columns: 1fr 1fr 1fr;
    }
}

@media (min-width:768px) {
    .menu-container {
        grid-template-columns: 100px auto;
    }
}

@media (min-width:992px) {
    .menu-content-body{
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
}

@media (min-width:1200px) {
    .menu-container {
        grid-template-columns: 200px auto;
    }
    .menu-content-body{
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
    }
}

.menu-item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 8px; 
    cursor: pointer;
    transition: all 0.3s ease-out;
    margin: 6px 10px;
    overflow: hidden;
}
.menu-item-warper{
    padding: 12px 3px;
}
.menu-item:hover {
    background-color:#f2f8fd;
    transform:translate(0,-5px);
    z-index:999;
    box-shadow:0px 0px 2px 2px #f2f8fd;
}

.menu-item:hover .menu-item-content > h4{
    color: #ff6969;
}
.menu-item:hover .menu-item-content > p{
    color: #525252;
}

.menu-item-header{
    display: grid;
    grid-template-columns: 40px 1fr;
    overflow: hidden;
}


.menu-item-header > * {
    min-width: 0;  /* 覆盖默认最小宽度 */
}

.menu-item-content{
    padding-left: 6px;
}

.menu-item-content > h4 {
    font-size: 14px;
    color: #3c3c3c;
    margin-bottom: 6px;
    overflow: hidden;
    white-space: nowrap;  /* 禁止换行 */
    text-overflow: ellipsis; /* 可选：溢出显示省略号 */
}
.menu-item-content > p {
    font-size: 12px;
    color: #8f8f8f;
    line-height: 1.5em;
    max-height: 36px;
    overflow: hidden;
}
.menu-item-footer {
    width: 100%;
    max-height: 32px;
    overflow: hidden;
    flex-wrap: wrap;
    white-space: nowrap;  /* 禁止换行 */
    text-overflow: ellipsis; /* 可选：溢出显示省略号 */
}
.menu-item-footer > span{
    font-size: 12px;
    padding: 2px 6px;
    background-color: beige;
    border-radius: 6px;
    margin: 6px 3px 0px 3px;
    flex-shrink: 0;
}

.menu-button{
    display: flex;
    height: 0px;
    overflow: hidden;
    transition: height 0.5s ease;
}

.menu-warp.active  .menu-button{
    height: 32px;
    border-top: 1px solid #e5e7eb;
}

.menu-button > button{
    background-color: transparent;
    width: 50%;
    border: none;
    padding: 6px 0;
    cursor: pointer;
    white-space: nowrap;/*文字不换行*/
    font-weight: bolder;
    display: flex; /* 使用 Flexbox 布局 */;
    align-items: center; /* 垂直居中 */
    justify-content: center;
}
.menu-button > button > svg{
    vertical-align: middle;
    margin-right: 6px;
}


.menu-button > button:first-child{
    color:#15803D;
}
.menu-button > button:first-child:hover{
    color:#389e0d;
}
.menu-button > button:last-child{
    color:#B91C1C;
}
.menu-button > button:last-child:hover{
    color:rgb(255, 114, 109);
}


.menu-content-edit{
    float: right;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #3c3c3c;
    cursor: pointer;
}
.menu-content-edit:hover{
    color: #ff6969;
}
