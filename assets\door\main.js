

const baseUrlApi = 'https://www.maran.fun/api/b/';
const verifyApi = baseUrlApi + 'nav/verify';
const navItemsApi = baseUrlApi + 'nav';
const navMenuItemsApi = baseUrlApi + 'nav/menu';

// 1. 定义页面全局数据
var navItems = [];
var navMenuItems = [];
var searchs = [];
var network = false;

localStorage.setItem('token',1); // 模拟登录状态，设置全局验证头
let token = localStorage.getItem('token');
if (isEmpty(token)) {
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
}

// 2. 使用缓存更新数据
navItems = isEmpty(localStorage.getItem('navItems')) ? navData : JSON.parse(localStorage.getItem('navItems'));
navMenuItems = isEmpty(localStorage.getItem('navMenuItems')) ? navMenuData : JSON.parse(localStorage.getItem('navMenuItems'));
searchs = isEmpty(localStorage.getItem('searchs')) ? searchData : JSON.parse(localStorage.getItem('searchs'));

// 3. 用完就清
navData = null;navMenuData = null;searchData = null;

// 4. 异步更新数据，无感更新，头部不重载
localStorage.setItem('searchs',JSON.stringify(searchs));
axios.get(navItemsApi).then(function(response){
  network = true;
  if(response.data.code === 200){
    navItems = response.data.data.list;
  }
  console.log('navItems',navItems);
}).finally(function(){ // 成功无所谓，都执行这一步，将数据更新到缓存
  localStorage.setItem('navItems', JSON.stringify(navItems));
})

axios.get(navMenuItemsApi).then(function(response){
  if(response.data.code === 200){
    navMenuItems = response.data.data.list;
  }
  console.log('navMenuItems',navMenuItems);
}).finally(function(){ // 成功无所谓，都执行这一步，将数据更新到缓存
  localStorage.setItem('navMenuItems', JSON.stringify(navMenuItems));
})





