  /* 禁止选择文字 */
  div{
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }

  body{
    margin: 0;
    padding: 0;
    width: 100hv;
    overflow: hidden;
  }

  .title{
    text-align: center;
    width: 100%;
    overflow: hidden;
    padding-top: 10px;
    z-index: -1;
  }
  .title>p{
    line-height: 36px;
    color: #909399;
    font-size: 1rem;
  }
  .content{
    position: absolute;
    top: 39%;
    width: 100%;
    height: 120px;
    text-align: center;
    z-index: 99;
  }
  .eat{
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 32px;
    color: #FF9733;
  }
  .start{
    display: inline-block;
    padding: 0 25px;
    outline: 0;
    border: #fff 3px solid;
    border-radius: 25px;
    background: rgba(0,0,0,.55);
    color: #fff;
    vertical-align: middle;
    font-size: 19px;
    line-height: 2;
    margin-bottom: 10px;
    cursor: pointer;
  }
  .start:hover{
    border-color: #C0C4CC;
  }
  .more>span{
    padding: 3px 9px;
    border-radius: 12px;
    background-color: #f5f5f5;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: .3s;
    margin: 5px;
    z-index: 1;
  }
  .more>span:hover{
    color: #000;
  }
  .child-text{
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
    animation: showM 1s 1;
    position: absolute;
    visibility: hidden;
    white-space: nowrap;  /*强制span不换行*/
  }

  @keyframes showM{
    from{
      visibility: visible;
      opacity: 1;
    }
    to{
      opacity: 0;
    }
  }
  .popup{
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: black;
    opacity: 0.5;
    animation: showPopup 300ms 1;
    z-index: 99;
  }
  @keyframes showPopup{
    from{
      opacity: 0;
    }
    to{
      opacity: 0.5;
    }
  }
  .popup-box{
    width: 300px;
    position: absolute;
    top: 30%;
    left: 50%;
    margin-left: -150px;
    width: 300px;
    text-align: center;
    background-color: #fff;
    z-index: 999;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 0 22px #ccc;
    animation: 300ms linear showBox  1;
  }
  @keyframes showBox{
    from{
      opacity: 0;
    }
    to{
      opacity: 1;
    }
  }
  .popup-box>textarea {
    padding: 5px;
    width: 96%;
    height: 200px;
    border-radius: 3px;
    font-size: 13px;
    font-family: "Microsoft YaHei";
    line-height: 17px;
  }
  .popup-box>h3{
    margin-bottom: 5px;
    text-align: center;
    font-weight: 600;
    font-size: 18px;
  }
  .popup-box>h3>small{
    display: block;
    color: #666;
    font-size: 12px;
    font-weight: 100;
    margin-top: 3px;
    margin-bottom: 0px;
  }
  .btn-ok{
    height: 30px;
    margin-top: 4px;
    padding: 6px 0;
    width: 100%;
    border: none;
    border-radius: 3px;
    background-color: #f90;
    color: #fff;
    font-size: 15px;
    -webkit-appearance: none;
  }

  .fonter{
    text-align: center;
    line-height: 60px;
    width: 100%;
    font-size: 13px;
    color: #909399;
    position: absolute;
    bottom: 0;
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }
  .footer-a{
    text-decoration: none;
    color: #909399;
  }
  .footer-a:hover{
    color: #606266;
  }
  .fonter-font{
    padding-left: 10px;
    margin-left: 5px;
    border-left:1px solid #909399;
    cursor: pointer;
  }

  .hide{
    display: none;
  }
  .show{
    display: inline;
  }

  .cuisine-title{
    padding-bottom: 6px;
    border-bottom:1px solid #909399;
  }
  .cuisine-menu{
    border-radius: 20px;
    font-size: 14px;
    line-height: 20px;
    padding: 3px 9px;
    background-color: #EBEEF5;
    margin: 4px 3px;
    cursor: pointer;
    display: inline-block;
  }
  .cuisine-menu:hover{
    background-color: #606266;
    color: white;
  }

  .note{
    line-height: 80px;
    font-size: 14px;
    color: #909399;
  }

  /*手机点击有红框，去红框*/
  a,button,input,div{
    -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
    -moz-tap-highlight-color:rgba(0, 0, 0, 0);
    -ms-tap-highlight-color:rgba(0, 0, 0, 0);
    -o-tap-highlight-color:rgba(0, 0, 0, 0);/*解决移动端点击显示背景框问题*/
  }