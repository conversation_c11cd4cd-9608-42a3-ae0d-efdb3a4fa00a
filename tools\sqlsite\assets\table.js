let editableColumns = [];
let editableTableName = "my_table";
let sqlVariants = {};
const typeOptions = [
  "VARCHAR", "INT", "BIGINT", "NUMERIC", "DATE", "DATETIME", "TIMESTAMP", "TEXT"
];
const constraintOptions = [
  { value: "", label: "无" },
  { value: "PRIMARY KEY", label: "主键" },
  { value: "NOT NULL", label: "非空" },
  { value: "UNIQUE", label: "唯一" },
  { value: "AUTO_INCREMENT", label: "自增" }
];

// SQL生成表格
function analyzeSQL() {
  const sqlRaw = document.getElementById('sqlInput').value;
  const resultDiv = document.getElementById('result');
  document.getElementById('sqlOutput').style.display = 'none';
  document.getElementById('sqlTabs').style.display = 'none';
  editableColumns = [];

  if (!sqlRaw.trim()) {
    resultDiv.innerHTML = '<p style="color:#e74c3c;">请输入SQL建表语句！</p>';
    return;
  }

  let sql = sqlRaw.trim();
  if (!/;\s*$/.test(sql)) sql += ';';
  sql = sql.replace(/creat\s+table/i, 'CREATE TABLE');
  sql = sql.replace(/varchar2?/ig, 'VARCHAR');
  sql = sql.replace(/number/ig, 'NUMERIC');
  sql = sql.replace(/（/g, '(').replace(/）/g, ')');

  const tableMatch = sql.match(/CREATE\s+TABLE\s+([`"\[]?)(\w+)\1\s*\(([\s\S]+?)\)[^)]*;/i)
    || sql.match(/CREATE\s+TABLE\s+([`"\[]?)(\w+)\1\s*\(([\s\S]+?)\)[^)]*$/i);
  if (!tableMatch) {
    resultDiv.innerHTML = '<p style="color:#e74c3c;">未识别到有效的CREATE TABLE语句。</p>';
    return;
  }
  const tableName = tableMatch[2];
  editableTableName = tableName;
  const fieldsStr = tableMatch[3];
  const lines = fieldsStr.split(/,(?![^\(\[]*[\]\)])/).map(f => f.trim()).filter(f => f);
  const fieldLines = lines.filter(line => !/^(PRIMARY|UNIQUE|KEY|CONSTRAINT|INDEX|FOREIGN|CHECK)\b/i.test(line));
  let html = `<h3>表名：<input id="editable-table-name" value="${tableName}" /></h3>`;
  html += `<table class="result-table" id="editable-table"><tr><th>字段名</th><th>类型</th><th>约束</th><th>操作</th></tr>`;
  let columns = [];
  fieldLines.forEach((field, idx) => {
    const m = field.match(/^([`"\[]?)(\w+)\1\s+([A-Z0-9_\(\), ]+)(.*)$/i);
    if (m) {
      const name = m[2];
      const type = m[3].trim();
      let constraintVal = "";
      if (/PRIMARY\s+KEY/i.test(field)) constraintVal = "PRIMARY KEY";
      else if (/NOT\s+NULL/i.test(field)) constraintVal = "NOT NULL";
      else if (/UNIQUE/i.test(field)) constraintVal = "UNIQUE";
      else if (/AUTO_INCREMENT|IDENTITY|SERIAL/i.test(field)) constraintVal = "AUTO_INCREMENT";
      html += `<tr>
        <td><input class="editable-col-name" data-idx="${idx}" value="${name}" /></td>
        <td>
          <select class="editable-col-type" data-idx="${idx}">
            ${typeOptions.map(opt => `<option value="${opt}" ${type.toUpperCase().startsWith(opt) ? 'selected' : ''}>${opt}</option>`).join('')}
          </select>
        </td>
        <td>
          <select class="editable-col-constraint" data-idx="${idx}">
            ${constraintOptions.map(opt => `<option value="${opt.value}" ${constraintVal === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
          </select>
        </td>
        <td><button class="del-col-btn" data-idx="${idx}" title="删除">删除</button></td>
      </tr>`;
      columns.push({
        name,
        type,
        rawType: type,
        constraint: constraintVal
      });
    }
  });
  html += `</table><button class="add-col-btn" onclick="addColumn()">+ 添加字段</button>`;
  resultDiv.innerHTML = html;
  editableColumns = columns;
  bindEditableEvents();
  sqlVariants = generateSQLVariants(editableTableName, editableColumns);
  document.getElementById('sqlTabs').style.display = 'block';
  showSQL('mysql');
}
