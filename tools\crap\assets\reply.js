var reply = [
  {
    "id": 57,
    "title": "回复语录",
    "content": "好事多磨/难以想象/过意不去/振作点儿/说的没错/习惯就好/都不容易/想开一点/听我一句劝/吃亏是福/多大点事/都是朋友/别太计较/算了算了/换位思考/你幸苦了/这人不行/我的妈呀/震惊我妈/下巴掉了",
    "annotation": "聊天回复",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 58,
    "title": "八卦",
    "content": "啊？这也太内个了吧/太过分了/咋能这样呢/这叫啥事啊/这人也真是/怎么回事/怎么能这样呢/哇/嚯/服了/详细说说/妈耶",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 59,
    "title": "不想听牢骚",
    "content": "咋会这样呢/对对对/好家伙/说的是啊/谁不是呢/真过意不去/好说好说/简直难以想象/有道理/真有你的/原来是这样/我就知道/还是你厉害，我就不行/我可以理解这是高级凡尔赛吗/开眼界了/还是要打起精神来/我也这么觉得/我也是",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 60,
    "title": "万能回复",
    "content": "好问题/会好的/笑死/真的耶/怎么啦/难搞哦/我懂/就是...（个人理解）/那也是/你知道我多想成为你吗",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 61,
    "title": "不想听吹牛",
    "content": "真的吗？好厉害啊/是吗/不愧是你/我觉得挺牛的/666/真行啊",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 62,
    "title": "拒绝闲聊",
    "content": "上班不忙吗/你不上班吗/你很无聊吗/最近很闲吗/人嘛/我觉得也是/可不是嘛/你说的没错/看你自己/也没啥意思/生活嘛/能这样/要用辩证的眼光看待事务",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 63,
    "title": "安慰",
    "content": "抱抱你，太心疼了/太生气了/好惨啊，咋能这样啊/我也生气了/无语了/咋欺负人呢/不难过哦会好起来的/硬着头皮上吧，慢慢来",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 64,
    "title": "鼓励对方说下去",
    "content": "为什么？/怎么会？/真的嘛？/我都不知道唉！/那怎么办？/后来呢？/原来是这样！",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  },
  {
    "id": 65,
    "title": "其他回复",
    "content": "我辈楷模/大佬大佬/学到了/interesting/nice/good/omg/那还挺好的/那就先这样/好像有点事儿/哈哈不用啦/确实，该干嘛就干嘛",
    "annotation": "",
    "classify": 1,
    "deleted": false,
    "createTime": "2022-10-29 12:28:37",
    "updateTime": "2022-10-29 12:28:37"
  }
]