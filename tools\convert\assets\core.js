function convert(){
  var value = document.getElementById("inputText").value
  value = value.replace(/[\r\n]/g,"<br>");
  if(value==''){return}
  var result = document.getElementById("result")
  result.innerHTML = "";
  addHistory(value);

  // 判断类型选择
  if(selectedTypes.has('rmb') && /^[1-9]\d{0,14}(\.\d{1,4})?$|^0(\.\d{1,4})?$/.test(value)){
    result.appendChild(createDiv("数值转银行大写",convertCurrency(value)))
  }
  if(selectedTypes.has('thousand') && /^\d+(?=\.{0,1}\d+$|$)/.test(value)){
    result.appendChild(createDiv("千位分隔符",formatPrice(value)))
  }
  if(selectedTypes.has('upper') && /^.*[a-z].*$/.test(value)){
    result.appendChild(createDiv("小写转大写",value.toUpperCase()))
  }
  if(selectedTypes.has('lower') && /^.*[A-Z].*$/.test(value)){
    result.appendChild(createDiv("大写转小写",value.toLowerCase()))
  }
  if(selectedTypes.has('swap') && /^.*[a-zA-Z].*$/.test(value)){
    result.appendChild(createDiv("大小写互转",transfers(value)))
  }
  if(selectedTypes.has('pinyinFull') && /^.*[\u4e00-\u9fa5].*$/.test(value)){
    result.appendChild(createDiv("文字转全写拼音",pinyin.getFullChars(value)))
  }
  if(selectedTypes.has('pinyinCamel') && /^.*[\u4e00-\u9fa5].*$/.test(value)){
    result.appendChild(createDiv("文字转简写拼音",pinyin.getCamelChars(value)))
  }
  if(selectedTypes.has('num') && /^.*\d.*$/.test(value)){
    result.appendChild(createDiv("提取字符中的数字",value.replace(/[^\d]/g,'')))
  }
  if(selectedTypes.has('hanzi') && /^.*[\u4e00-\u9fa5].*$/.test(value)){
    result.appendChild(createDiv("提取字符中的汉字",value.replace(/[^\u4E00-\u9FA5]/g,'')))
  }
  if(selectedTypes.has('alpha') && /^.*[a-zA-Z].*$/.test(value)){
    result.appendChild(createDiv("提取字符中的字母",value.replace("<br>","").replace(/[^a-zA-Z]/g,'')))
  }
  if(selectedTypes.has('stat')){
    let countArr = wordCount(value)
    let countStr = "共计：<button style='--i: 1'>" + countArr[0] + "个字数</button><button style='--i: 2'>" + countArr[1] + "个字符</button><br>"
      + "包含：<button style='--i: 3'>" + countArr[2] + "个汉字</button><button style='--i: 4'>"+ countArr[3] + "个标点（全角）</button><button style='--i: 5'>"
      + countArr[4] + "个字母</button><button  style='--i: 6'>" + countArr[5] + "个数字</button>"
    result.appendChild(createDiv("统计",countStr))
  }
  result.scrollIntoView({behavior: "smooth", block: "start"});
}