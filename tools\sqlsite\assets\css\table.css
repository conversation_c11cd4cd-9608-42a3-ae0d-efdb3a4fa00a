.result-table { width: 100%; border-collapse: collapse; margin-top: 1.5em; }
.result-table th, .result-table td { border: 1px solid #e3e8f7; padding: 8px 10px; text-align: left; }
.result-table th { background: #f0f6ff; color: #357ae8; }
.result-table tr:nth-child(even) { background: #f8fafd; }
.add-col-btn, .del-col-btn {
    margin-top: 4px;
    color: #357ae8;
    background: #eaf3ff;
    border: 1px solid #d0d7de;
    border-radius: 4px;
    padding: 2px 10px;
    cursor: pointer;
}
.del-col-btn { color: #e74c3c; background: #fff0f0; border: 1px solid #f5c6cb; }
.table-edit-btns { margin-bottom: 1em; }
.table-edit-btns button { margin-right: 10px; }