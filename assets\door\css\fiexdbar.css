
.fixed-bar{
    position: fixed;
    right: 1rem;
    bottom: 3rem;
    z-index: 600;
    width: 40px;
    display: flex;
    flex-direction: column;
}
.fixed-bar > div{
    width: 40px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    overflow: hidden;
}
.fixed-bar > .fixed-bar-up{
    background-color: rgba(140, 140, 140, 0.7);
    backdrop-filter: saturate(180%) blur(20px);
    height: 0px;
    transition: all .4s, transform .4s;
}
.fixed-bar >  .fixed-bar-up.show{
    height: 40px;
}
.fixed-bar-up:hover {
    transform: translateY(-.25rem);
}
.fixed-bar-item{
    height: 40px;
    transition: bottom .4s, transform .4s;
}
.fixed-bar-item > button{
    color: #fff;
    border: none;
    background-color: rgba(140, 140, 140, 0.7);
    backdrop-filter: saturate(180%) blur(20px);
    width: 100%;
    height: 100%;
    cursor: pointer;
}


#fixed-bar-edit.active{
    background-color: rgba(245, 34, 45, 0.7);
}

.fixed-bar-item:hover {
    transform: translateY(-.25rem);
    background-color: rgba(20, 20, 20, 0.7);
}
