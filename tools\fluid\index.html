<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>瀑布流布局示例</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
    body {
      background: #222;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      color: #fff;
      font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    }
    .masonry {
      column-count: 3;
      column-gap: 1.2rem;
      max-width: 1200px;
      margin: 0 auto;
      padding: 1rem;
    }
    @media (max-width: 900px) {
      .masonry { column-count: 2; }
      .masonry-item { width: 48vw; }
    }
    @media (max-width: 600px) {
      .masonry { column-count: 1; }
      .masonry-item { width: 98vw; max-width: 100%; }
    }
    .masonry-item {
      background: #333;
      border-radius: 12px;
      margin-bottom: 1rem;
      display: inline-block;
      width: 370px;
      box-sizing: border-box;
      box-shadow: 0 2px 8px #0006;
      overflow: hidden;
      break-inside: avoid;
      transition: box-shadow 0.2s;
      position: relative;
      border: none;
      opacity: 1;
      animation: fadeInItem 0.5s;
      vertical-align: top;
    }
    @keyframes fadeInItem {
      0% { opacity: 0; transform: scale(0.92) translateY(30px);}
      100% { opacity: 1; transform: scale(1) translateY(0);}
    }
    .masonry-item img {
      width: 100%;
      height: auto;
      display: block;
      border-radius: 12px 12px 0 0;
      background: #444;
      object-fit: contain;
      aspect-ratio: unset;
      max-height: 420px;
      filter: brightness(0.92) blur(2px);
      opacity: 0;
      transition: filter 0.5s, opacity 0.5s;
    }
    .masonry-item img.loaded {
      filter: none;
      opacity: 1;
    }
    .masonry-item .desc {
      padding: 0.7rem 1rem;
      font-size: 1rem;
      color: #ffd700;
      text-shadow: 1px 1px 0 #000;
      text-align: center;
      border-top: 1px solid #ffd70044;
      background: #222;
      border-radius: 0 0 10px 10px;
    }
    .masonry-item:hover {
      box-shadow: 0 6px 24px #000a;
    }
    .upload-btn-wrapper, .clear-btn-wrapper {
      position: fixed;
      top: 24px;
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .upload-btn-wrapper { right: 32px; }
    .clear-btn-wrapper { right: 100px; }
    .upload-btn, .clear-btn {
      color: #ffd700;
      background: #333;
      border: 1.5px solid #ffd700;
      border-radius: 50%;
      width: 54px;
      height: 54px;
      font-size: 1.7rem;
      cursor: pointer;
      box-shadow: 0 2px 12px #000a;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.2s, box-shadow 0.2s;
      outline: none;
      padding: 0;
      position: relative;
      margin-left: 10px;
    }
    .upload-btn:hover, .upload-btn:focus,
    .clear-btn:hover, .clear-btn:focus {
      background: #444;
      box-shadow: 0 4px 24px #000c;
    }
    .upload-btn input[type="file"] {
      opacity: 0;
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0; top: 0;
      cursor: pointer;
    }
    .empty-tip {
      text-align: center;
      color: #ffd700;
      font-size: 1.3rem;
      margin: 3rem 0 2rem 0;
      letter-spacing: 2px;
      opacity: 0.7;
      user-select: none;
    }
    @media (max-width: 600px) {
      .upload-btn-wrapper { top: 12px; right: 12px; }
      .clear-btn-wrapper { top: 12px; right: 70px; }
      .upload-btn, .clear-btn {
        width: 44px;
        height: 44px;
        font-size: 1.2rem;
      }
      .masonry-item { width: 98vw; max-width: 100%; }
    }
  </style>
</head>
<body>
  <div class="upload-btn-wrapper">
    <label class="upload-btn" title="导入图片">
      <span>＋</span>
      <input type="file" id="imgInput" accept="image/*" multiple directory webkitdirectory />
    </label>
  </div>
  <div class="clear-btn-wrapper">
    <button class="clear-btn" id="clearBtn" title="清空导入图片">🗑</button>
  </div>
  <div class="masonry" id="masonry"></div>
  <div id="emptyTip" class="empty-tip" style="display:none;">暂无图片，请点击右上角“＋”导入</div>
  <script>
    // 本地存储键
    const STORAGE_KEY = 'fluid_masonry_imgs_v1';

    // 读取本地数据
    function loadFromStorage() {
      try {
        const data = localStorage.getItem(STORAGE_KEY);
        if (data) {
          return JSON.parse(data);
        }
      } catch {}
      return [];
    }

    // 保存到本地
    function saveToStorage(items) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(items));
      } catch {}
    }

    // 初次加载时优先读取本地 localStorage
    let allItems = loadFromStorage();

    const masonry = document.getElementById('masonry');
    const emptyTip = document.getElementById('emptyTip');

    function renderMasonry(newIndexes = []) {
      if (!allItems.length) {
        masonry.innerHTML = '';
        emptyTip.style.display = '';
        return;
      }
      emptyTip.style.display = 'none';
      masonry.innerHTML = allItems.map((item, idx) => {
        let anim = newIndexes.includes(idx)
          ? `style="animation:fadeInItem 0.5s cubic-bezier(.4,2,.6,1) ${0.04 * newIndexes.indexOf(idx)}s"`
          : '';
        return `
          <div class="masonry-item" ${anim}>
            <img src="${item.img}" alt="">
            <div class="desc">${item.desc || ''}</div>
          </div>
        `;
      }).join('');
    }

    renderMasonry();

    // 支持本地文件夹和多文件叠加导入
    document.getElementById('imgInput').addEventListener('change', function(e) {
      const files = Array.from(e.target.files);
      if (!files.length) return;
      const imgFiles = files.filter(f => f.type.startsWith('image/'));
      if (!imgFiles.length) return;
      // 新增图片追加到已有数据前面
      let loaded = 0;
      const newItems = [];
      imgFiles.forEach((file, i) => {
        const reader = new FileReader();
        reader.onload = function(evt) {
          newItems.push({
            img: evt.target.result,
            desc: file.webkitRelativePath || file.name
          });
          loaded++;
          if (loaded === imgFiles.length) {
            allItems = newItems.concat(allItems); // 追加，不清空
            saveToStorage(allItems);
            const newIndexes = [];
            for (let j = 0; j < imgFiles.length; j++) newIndexes.push(j);
            renderMasonry(newIndexes);
          }
        };
        reader.readAsDataURL(file);
      });
    });

    // 清空本地缓存
    document.getElementById('clearBtn').onclick = function() {
      if (confirm('确定要清空所有已导入的图片吗？')) {
        allItems = [];
        saveToStorage(allItems);
        renderMasonry();
      }
    };
  </script>
</body>
</html>