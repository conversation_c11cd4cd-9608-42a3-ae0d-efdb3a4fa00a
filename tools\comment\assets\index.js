// 获取链接参数
function getQueryParam(name) {
  const params = new URLSearchParams(window.location.search);
  return params.get(name);
}

// 动态设置菜单选项
(function(){
  const sel = document.getElementById('menuSelect');
  // 获取 ?menu=xxx,xxx,xxx
  let typeParam = getQueryParam('type');
  let typeList;
  if (typeParam) {
    typeList = typeParam.split(',').map(s => s.trim()).filter(Boolean);
    if (typeList.length === 0) typeList = ['对作者的评价'];
  } else {
    typeList = ['对作者的评价'];
  }
  // 全局变量覆盖
  window.MENU_LIST = typeList;
  sel.innerHTML = '<option value="">请选择要评价的内容</option>' + typeList.map(m=>`<option value="${m}">${m}</option>`).join('');
})();












