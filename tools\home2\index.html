<!DOCTYPE html>
<html lang="en">
<head>
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/icon.png" type="image/x-icon"/>
  <title>茫茫然</title>
</head>
</head>
<body>
  <div class="container">
    <div class="card">
      <div class="content">
        <h2>27</h2>
        <h3>现在的我</h3>
        <p>善良 憨厚 谦虚 正直 有趣 慷慨 真诚 勇敢 自信 努力 可靠 实在 自然 谦逊 友爱 坚定</p>
        <a href="#" style="--i: 1">Go See</a>
      </div>
    </div>
    <div class="card">
      <div class="content">
        <h2>26</h2>
        <h3>过去的我</h3>
        <p>狡诈 虚伪 残忍 卑鄙 贪婪 任性 蛮横 自私 虚荣 龌龊 阴险 轻蔑 冷漠 伪善 庸俗 无耻</p>
        <a href="#" style="--i: 2">Go Study</a>
      </div>
    </div>
	</div>
  <div class="footer">@2022 | 备案号：湘ICP备2022012079号 | 关于</div>
</body>
<script>
  document.oncontextmenu = function () { return false };/* 禁止右键 */
</script>
<style>
.footer{
  position: absolute;
  bottom: 1rem;
  line-height: 2rem;
  color: #AAA;
  text-align: center;
  font-size: 12px;
}
*{margin: 0;padding: 0;box-sizing: border-box;}/* 初始化 */
body{
  min-height: 100vh; /* 100%窗口高度 */
  display: flex;justify-content: center;align-items: center;background-color: #161626;/* 弹性布局+水平垂直居中 */
  --btn-bgcolor: #0EBEFE;
}
body::before{
  content: "";position: absolute;top: 0;left: 0;width: 100%;height: 100%;
  background: linear-gradient(90deg,#03a9f4,#f441a5,#ffeb3b,#03a9f4);
  clip-path: circle(30% at right 70%); /* 将元素剪切微一个圆形【30%表示圆的直径】【right 70%表示圆心位置】 */
} /* 给背景增加两个渐变圆 */
body::after{
  content: "";position: absolute;top: 0;left: 0;width: 100%;height: 100%;
  background: linear-gradient(90deg,#03a9f4,#f441a5,#ffeb3b,#03a9f4);
  clip-path: circle(20% at 10% 10%); /* 将元素剪切微一个圆形【30%表示圆的直径】【right 70%表示圆心位置】 */
} /* 给背景增加两个渐变圆 */
.container{ position: relative;display: flex;justify-content: center;align-items: center;flex-wrap: wrap;z-index: 1;}
.container .card{
  position: relative;overflow: hidden;display: flex;justify-content: center;align-items: center;cursor: pointer;
  width: 280px;height: 400px;margin: 30px;border-radius: 15px;
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 20px 20px 50px rgba(0, 0, 0, 0.5);
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  border-left: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px); /*背景模糊*/
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  -moz-tap-highlight-color:rgba(0, 0, 0, 0);
  -ms-tap-highlight-color:rgba(0, 0, 0, 0);
  -o-tap-highlight-color:rgba(0, 0, 0, 0);/*解决移动端点击显示背景框问题*/
}

.container .card .content{padding: 20px;text-align: center;transform: translateY(100px);opacity: 0;transition: 0.5s;}
.container .card:hover .content{
  transform: translateY(0px);opacity: 1;
}/*鼠标移入，上移+显示*/
.container .card .content h2{position: absolute;top: -80px;right: 25px;font-size: 128px;color: rgba(255, 255, 255, 0.05);}
.container .card .content h3{font-size: 28px;color: #fff;}
.container .card .content p{font-size: 16px;color: #fff;font-weight: 300;margin: 10px 0 15px;}
.container .card .content a{
  position: relative;display: block;width: 140px;height: 60px;line-height: 60px;text-align: center;color: var(--btn-bgcolor);font-size: 20px;
  margin: auto; text-decoration: none;
  transition: all 0.3s ease-in-out; /*加一下过度动画，下面后面的0.3秒是延迟*/
  /*hue-rotate是颜色滤镜，可以加不同的度数来改变颜色，这里我们使用calc自动计算方法还要var函数来调用我们给每一个a设置不同的属性值1~5然后分别乘以60度就能分别得到不同的度数*/
 filter: hue-rotate(calc(var(--i)*60deg));
}
.container .card .content a::before,
.container .card .content a::after{content: "";position: absolute;width: 20px;height: 20px;border: 2px solid var(--btn-bgcolor);transition: all 0.3s ease-in-out 0.3s;}
.container .card .content a::before {top: 0;left: 0;border-right: 0;border-bottom: 0;}
.container .card .content a::after {right: 0;bottom: 0;border-top: 0;border-left: 0;}
.container .card .content a:hover {
  background-color: var(--btn-bgcolor);
  color: #fff;box-shadow: 0 0 50px var(--btn-bgcolor);/*加个发光效果和下面的倒影，模糊度加到50px*/
  -webkit-box-reflect: below 1px linear-gradient(transparent,rgba(0,0,0,0.3));
  transition-delay: 0.4s;/*设置以上事件的过度时间*/
}
.container .card .content a:hover::before,.container .card .content a:hover::after{
  width: 138px;
  height: 58px;
  transition-delay: 0s;/*设置以上事件的过度时间*/
}
</style>
</html>