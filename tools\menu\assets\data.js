window.menuData = [
  {
    "id": "1",
    "title": "常用二维码",
    "content": "qrcode",
    "url": "qrcode",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <rect x="6" y="6" width="12" height="12" rx="2" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <rect x="30" y="6" width="12" height="12" rx="2" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <rect x="6" y="30" width="12" height="12" rx="2" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <rect x="20" y="20" width="8" height="8" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <rect x="32" y="32" width="4" height="4" fill="#ffd700" stroke="#222" stroke-width="2"/>
    </svg>`
  },
  {
    "id": "2",
    "title": "云风扇",
    "content": "fan",
    "url": "fan",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <circle cx="24" cy="24" r="8" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <path d="M24 16 Q38 8 32 24" stroke="#ffd700" stroke-width="2" fill="none"/>
      <path d="M24 32 Q10 40 16 24" stroke="#ffd700" stroke-width="2" fill="none"/>
      <path d="M32 24 Q40 38 24 32" stroke="#ffd700" stroke-width="2" fill="none"/>
      <path d="M16 24 Q8 10 24 16" stroke="#ffd700" stroke-width="2" fill="none"/>
    </svg>`
  },
  {
    "id": "3",
    "title": "今天吃啥呀",
    "content": "eat",
    "url": "eat",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <ellipse cx="24" cy="32" rx="14" ry="8" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <ellipse cx="24" cy="20" rx="8" ry="8" fill="#fff" stroke="#222" stroke-width="2"/>
      <rect x="22" y="10" width="4" height="8" fill="#ffd700" stroke="#222" stroke-width="2"/>
    </svg>`
  },
  {
    "id": "4",
    "title": "智能转换",
    "content": "convert",
    "url": "convert",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <path d="M12 24h24" stroke="#ffd700" stroke-width="3" stroke-linecap="round"/>
      <path d="M30 16l6 8-6 8" stroke="#ffd700" stroke-width="3" fill="none"/>
      <circle cx="16" cy="24" r="4" fill="#fff" stroke="#222" stroke-width="2"/>
    </svg>`
  },
  {
    "id": "5",
    "title": "时间线",
    "content": "timeline",
    "url": "timeline",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <rect x="10" y="10" width="28" height="6" rx="3" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <rect x="10" y="32" width="28" height="6" rx="3" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <circle cx="24" cy="24" r="5" fill="#fff" stroke="#222" stroke-width="2"/>
    </svg>`
  },
  {
    "id": "6",
    "title": "赛尔号",
    "content": "seer",
    "url": "seer",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <ellipse cx="24" cy="28" rx="14" ry="10" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <ellipse cx="24" cy="20" rx="8" ry="8" fill="#fff" stroke="#222" stroke-width="2"/>
      <circle cx="24" cy="20" r="3" fill="#ffd700" stroke="#222" stroke-width="1.5"/>
    </svg>`
  },
  {
    "id": "11",
    "title": "计时器",
    "content": "timer",
    "url": "timer",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <circle cx="24" cy="24" r="18" fill="#fff" stroke="#ffd700" stroke-width="3"/>
      <path d="M24 24v-10" stroke="#222" stroke-width="2"/>
      <path d="M24 24h8" stroke="#222" stroke-width="2"/>
    </svg>`
  },
  {
    "id": "12",
    "title": "房贷计算器",
    "content": "loan",
    "url": "loan",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <rect x="10" y="14" width="28" height="20" rx="6" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <text x="24" y="30" text-anchor="middle" font-size="12" fill="#222">¥</text>
    </svg>`
  },
  {
    "id": "13",
    "title": "个人所得税",
    "content": "tax",
    "url": "tax",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <rect x="12" y="14" width="24" height="20" rx="6" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <text x="24" y="30" text-anchor="middle" font-size="12" fill="#222">税</text>
    </svg>`
  },
  {
    "id": "14",
    "title": "流体图片",
    "content": "fluid",
    "url": "fluid",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <ellipse cx="24" cy="28" rx="14" ry="8" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <ellipse cx="24" cy="20" rx="8" ry="8" fill="#fff" stroke="#222" stroke-width="2"/>
      <path d="M16 24 Q24 36 32 24" stroke="#ffd700" stroke-width="2" fill="none"/>
    </svg>`
  },
  {
    "id": "16",
    "title": "密码管理器",
    "content": "lastpass",
    "url": "lastpass",
    "svg": `<svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <rect x="12" y="18" width="24" height="18" rx="6" fill="#ffd700" stroke="#222" stroke-width="2"/>
      <circle cx="24" cy="27" r="4" fill="#fff" stroke="#222" stroke-width="2"/>
      <rect x="20" y="10" width="8" height="8" rx="4" fill="#fff" stroke="#222" stroke-width="2"/>
    </svg>`
  }
]