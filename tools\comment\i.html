<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <link rel="stylesheet" href="../convert/assets/convert.css">
  <title>贴吧风格评论区</title>
  <style>
    .comment-board { max-width: 600px; margin: 2rem auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #eee; padding: 2rem 1.5rem; }
    .comment-title { font-size: 1.5rem; color: #4f364a; margin-bottom: 1.2rem; }
    .comment-form { display: flex; flex-direction: column; gap: 10px; margin-bottom: 1.5rem; }
    .comment-form input, .comment-form textarea { border: 2px solid #b596b5; border-radius: 8px; padding: 8px 12px; font-size: 1rem; }
    .comment-form textarea { resize: vertical; min-height: 60px; }
    .comment-form button { align-self: flex-end; background: #b596b5; color: #fff; border: none; border-radius: 8px; padding: 8px 24px; font-size: 1rem; cursor: pointer; transition: background 0.2s; }
    .comment-form button:hover { background: #4f364a; }
    .comment-list { margin: 0; padding: 0; list-style: none; }
    .comment-item { border-bottom: 1px dashed #eee; padding: 1rem 0 0.5rem 0; }
    .comment-header { display: flex; align-items: center; gap: 10px; }
    .comment-username { color: #4f8cff; font-weight: bold; }
    .comment-time { color: #aaa; font-size: 0.9em; }
    .comment-content { margin: 0.5em 0 0.5em 0; color: #333; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; }
    .comment-actions { display: flex; gap: 16px; align-items: center; font-size: 0.95em; }
    .like-btn { color: #b596b5; background: none; border: none; cursor: pointer; font-weight: bold; }
    .like-btn.liked { color: #f56c6c; }
    .reply-btn { color: #409eff; background: none; border: none; cursor: pointer; }
    .reply-form { margin-top: 0.5em; }
    .reply-list { margin-left: 2em; }
    .sensitive-tip { color: #f56c6c; font-size: 0.95em; margin-top: 0.2em; }
    .reply-info { color: #888; font-size: 0.95em; margin-bottom: 2px; }
    .comment-select { width: 100%; }
    .collapse-btn {
      background: none;
      border: none;
      color: #b596b5;
      cursor: pointer;
      font-size: 0.98em;
      margin: 0.5em 0;
      padding: 0;
    }
  </style>
</head>
<body style="background-color: rgb(251, 238, 230);">
  <div class="comment-board">
    <div class="comment-title">贴吧风格评论区</div>
    <form class="comment-form" id="mainCommentForm" autocomplete="off">
      <input type="text" id="username" maxlength="16" placeholder="昵称（必填）" required>
      <textarea id="content" maxlength="300" placeholder="写下你的评论..." required></textarea>
      <div class="sensitive-tip" id="mainSensitiveTip" style="display:none;"></div>
      <button type="submit">发表评论</button>
    </form>
    <ul class="comment-list" id="commentList"></ul>
  </div>
  <script>
    // ====== 配置区 ======
    const SENSITIVE_WORDS = ['傻', 'sb', '垃圾', '妈的', 'fuck', '操', '死', '滚', '狗', '草', 'cnm', 'nmsl'];
    const MAX_REPLY = 3; // 回复最多显示3条，超出可展开
    // 通过 ?type=xxx 控制评论类型
    function getQueryParam(name) {
      const params = new URLSearchParams(window.location.search);
      return params.get(name);
    }
    const COMMENT_TYPE = getQueryParam('type') || 'default';

    // ====== 工具函数 ======
    function hasSensitive(str) {
      return SENSITIVE_WORDS.some(word => str.toLowerCase().includes(word));
    }
    function filterSensitive(str) {
      let s = str;
      SENSITIVE_WORDS.forEach(word => {
        const reg = new RegExp(word, 'gi');
        s = s.replace(reg, '*'.repeat(word.length));
      });
      return s;
    }
    function getComments() {
      // 只取当前类型的评论
      return (JSON.parse(localStorage.getItem('tieba_comments') || '[]')).filter(c => c.type === COMMENT_TYPE);
    }
    function saveComments(list) {
      // 合并其它类型的评论
      let all = JSON.parse(localStorage.getItem('tieba_comments') || '[]');
      all = all.filter(c => c.type !== COMMENT_TYPE).concat(list);
      localStorage.setItem('tieba_comments', JSON.stringify(all));
    }
    function formatTime(ts) {
      const d = new Date(ts);
      return d.getFullYear() + '-' + (d.getMonth()+1).toString().padStart(2,'0') + '-' + d.getDate().toString().padStart(2,'0') +
        ' ' + d.getHours().toString().padStart(2,'0') + ':' + d.getMinutes().toString().padStart(2,'0');
    }

    // ====== 渲染评论 ======
    function renderComments() {
      const list = getComments();
      const ul = document.getElementById('commentList');
      ul.innerHTML = '';
      // 只显示parent为null的主评论
      list.filter(c=>!c.parent).forEach(comment => {
        ul.appendChild(renderCommentItem(comment, list));
      });
    }

    // ====== 渲染单条评论（贴吧风格，回复平铺） ======
    function renderCommentItem(comment, allList) {
      const li = document.createElement('li');
      li.className = 'comment-item';
      li.innerHTML = `
        <div class="comment-header">
          <span class="comment-username">${comment.username}</span>
          <span class="comment-time">${formatTime(comment.createTime)}</span>
        </div>
        <div class="comment-content">${comment.content}</div>
        <div class="comment-actions">
          <button class="like-btn${comment.likes ? ' liked' : ''}">👍 <span>${comment.likes||0}</span></button>
          <button class="reply-btn">回复</button>
        </div>
      `;
      // 点赞
      li.querySelector('.like-btn').onclick = function() {
        if (comment.liked) return;
        comment.likes = (comment.likes||0) + 1;
        comment.liked = true;
        saveComments(allList);
        renderComments();
      };
      // 回复
      li.querySelector('.reply-btn').onclick = function() {
        showReplyForm(li, comment, allList);
      };

      // 渲染所有回复（全部平铺，且支持收起/展开）
      const replies = allList.filter(c=>c.parent===comment.id);
      if (replies.length) {
        const replyUl = document.createElement('ul');
        replyUl.className = 'reply-list';
        let showAll = replies._showAll || false;
        let visibleReplies = replies;
        if (replies.length > MAX_REPLY && !showAll) {
          visibleReplies = replies.slice(0, MAX_REPLY);
        }
        visibleReplies.forEach(reply => {
          replyUl.appendChild(renderReplyItem(reply, comment, allList));
        });

        if (replies.length > MAX_REPLY) {
          const btn = document.createElement('button');
          btn.className = 'collapse-btn';
          btn.textContent = showAll ? '收起回复' : `查看更多回复（${replies.length - MAX_REPLY}）`;
          btn.onclick = function() {
            replies._showAll = !showAll;
            renderComments();
          };
          replyUl.appendChild(btn);
        }
        li.appendChild(replyUl);
      }
      return li;
    }

    // ====== 渲染回复项（全部平铺） ======
    function renderReplyItem(reply, parentComment, allList) {
      const li = document.createElement('li');
      li.className = 'comment-item';
      li.innerHTML = `
        <div class="reply-info">${reply.username} 回复 ${parentComment.username}</div>
        <div class="comment-content">${reply.content}</div>
        <div class="comment-actions">
          <button class="like-btn${reply.likes ? ' liked' : ''}">👍 <span>${reply.likes||0}</span></button>
          <button class="reply-btn">回复</button>
        </div>
      `;
      // 点赞
      li.querySelector('.like-btn').onclick = function() {
        if (reply.liked) return;
        reply.likes = (reply.likes||0) + 1;
        reply.liked = true;
        saveComments(allList);
        renderComments();
      };
      // 回复
      li.querySelector('.reply-btn').onclick = function() {
        showReplyForm(li, reply, allList);
      };
      return li;
    }

    // ====== 显示回复表单 ======
    function showReplyForm(parentLi, parentComment, allList) {
      if (parentLi.querySelector('.reply-form')) return;
      const form = document.createElement('form');
      form.className = 'reply-form comment-form';
      form.innerHTML = `
        <input type="text" class="reply-username" maxlength="16" placeholder="昵称（必填）" required>
        <textarea class="reply-content" maxlength="300" placeholder="写下你的回复..." required></textarea>
        <div class="sensitive-tip" style="display:none;"></div>
        <button type="submit">回复</button>
      `;
      form.onsubmit = function(e) {
        e.preventDefault();
        const username = form.querySelector('.reply-username').value.trim();
        const content = form.querySelector('.reply-content').value.trim();
        const tip = form.querySelector('.sensitive-tip');
        if (!username || !content) return;
        if (hasSensitive(content)) {
          tip.textContent = '含有敏感词，请修改后再提交。';
          tip.style.display = '';
          return;
        }
        const list = getComments();
        list.push({
          id: Date.now() + Math.floor(Math.random()*1000),
          username,
          parent: parentComment.id,
          content: filterSensitive(content),
          createTime: Date.now(),
          likes: 0,
          type: COMMENT_TYPE
        });
        saveComments(list);
        renderComments();
      };
      parentLi.appendChild(form);
    }

    // ====== 主评论表单 ======
    document.getElementById('mainCommentForm').onsubmit = function(e) {
      e.preventDefault();
      const username = document.getElementById('username').value.trim();
      const content = document.getElementById('content').value.trim();
      const tip = document.getElementById('mainSensitiveTip');
      if (!username || !content) return;
      if (hasSensitive(content)) {
        tip.textContent = '含有敏感词，请修改后再提交。';
        tip.style.display = '';
        return;
      }
      tip.style.display = 'none';
      const list = getComments();
      list.push({
        id: Date.now() + Math.floor(Math.random()*1000),
        username,
        parent: null,
        content: filterSensitive(content),
        createTime: Date.now(),
        likes: 0,
        type: COMMENT_TYPE
      });
      saveComments(list);
      this.reset();
      renderComments();
    };

    // ====== 初始化 ======
    window.addEventListener('DOMContentLoaded', function() {
      renderComments();
    });
  </script>
</body>
</html>