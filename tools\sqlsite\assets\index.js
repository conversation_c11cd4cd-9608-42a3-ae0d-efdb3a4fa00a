
// 错误纠正
function autoCorrectSQL(sql) {
  let corrected = sql.trim();
  if (!/;\s*$/.test(corrected)) corrected += ';';
  corrected = corrected.replace(/creat\s+table/i, 'CREATE TABLE');
  corrected = corrected.replace(/varchar2?/ig, 'VARCHAR');
  corrected = corrected.replace(/number/ig, 'NUMERIC');
  corrected = corrected.replace(/（/g, '(').replace(/）/g, ')');
  return corrected;
}
// let sql = autoCorrectSQL(sqlRaw);
// let errorTip = '';
// if (sql !== sqlRaw) {
//     errorTip = '<div class="error-tip">已自动纠正部分SQL格式或关键字，请检查。</div>';
// }






















