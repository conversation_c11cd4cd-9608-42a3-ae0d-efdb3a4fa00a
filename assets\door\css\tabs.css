
.tabs-container{
    height: auto;
    margin-bottom: 20px;
    overflow: hidden;
}

.tabs{
    width: 100%;
    border: 1px solid rgba(53, 52, 52, 0.226);
    border-radius: 8px;
}

.swiper-slide{
    position: relative;
    background-color: #fff;
    color: #000000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE和Edge */
    scroll-snap-type: x mandatory; /* 启用滚动捕捉 */
    scroll-behavior: smooth; /* 平滑滚动 */
    user-select: none;
    justify-content: center;
}

.swiper-slide > label {
    text-align: center;
    flex: 0 0 var(--tabWidth);
    cursor: pointer;
    z-index: 1;
    font-weight: 600;
    letter-spacing: -1px;
    font-size: 14px;
    white-space: nowrap; /* 防止内容换行 */
    line-height: 40px;
    scroll-snap-align: start;

}

.swiper-slide > label > input {
    display: none;
}

.selection{
    width:50px;
	height:3px;
	position:absolute;
	bottom:0px;
}

.selection .color {
	width:60%;
	margin:0 auto;
	height:3px;
	background:#ff4891;
}


.tabs-page-item {
    display: flex;
    border-radius: 8px;
    padding: 8px 3px;
    font-family: "Poppins", sans-serif;
    user-select: none;
}

.tabs-page-item > input {
    display: none;
}

.tabs-page-item > label {
    cursor: pointer;
    padding: 5px 8px;
    border-radius: 8px;
    font-size: 12px;
    transition: 0.2s ease-in-out;
    white-space: nowrap; /* 防止内容换行 */
    height: 16px;
}
.tabs-page-item > label:hover {
    background-color: #e6eef9;
    border-radius: 4px;
  }

.tabs-page-item > input:checked + label {
    background-color: #e6eef9;
    color: #185ee0;
}

#fromPageSwiper .swiper-container{
    width: 100%;
}

#fromPageSwiper .swiper-container .swiper-slide{
    justify-content: start;
    flex-wrap:wrap;
}
#fromPageSwiper .swiper-slide{
    border-bottom: none;
}