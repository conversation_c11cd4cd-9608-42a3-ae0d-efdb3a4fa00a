<!DOCTYPE html>
<html lang="zh-cmn-hans">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <title>任意门</title>
  <link rel="shortcut icon" href="assets/icon/logo.png" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/utils/swiper/swiper.css" />
  <link rel="stylesheet" href="assets/door/css/navbar.css" />
  <link rel="stylesheet" href="assets/door/css/menu.css" />
  <link rel="stylesheet" href="assets/door/css/sidebar.css" />
  <link rel="stylesheet" href="assets/door/css/fiexdbar.css" />
  <link rel="stylesheet" href="assets/door/css/modal.css" />
  <link rel="stylesheet" href="assets/door/css/tabs.css" />
  <link rel="stylesheet" href="assets/door/css/search.css" />
  <link rel="stylesheet" href="assets/door/css/toast.css" />
  <link rel="stylesheet" href="assets/door/css/dialog.css" />
</head>
<body >
<div id="app">
  <div class="nav-container">
    <div class="nav-header">
      <div class="nav-logo">
        <div class="nav-logo-img">
          <!-- <img src="assets/icon/my.png" height="40px" alt="任意门"> -->
        </div>
      </div>
      <div class="navbar">
        <h4 id="navbar">
          <a target="_self" name="nav" data-id="10" class="nav-item" onclick="clickNavbar(10)">未整理</a>
        </h4>
      </div>
    </div>
  </div> <!-- nav-container end -->
  <div class="search-container">
    <div class="search-header">
        
    </div>
    <div class="search-content">
      <form class="search-wrapper">
        <div id="search-icon">
          <img id="search-img" width="32" height="32" src="assets/svg/baidu.svg">
        </div>
        <input type="text" class="search-box" autocomplete="off" id="search-input" placeholder="搜索..." data-url="https://www.baidu.com/s?wd=">
        <div class="search-clear" id="search-clear">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
        <div class="search-btn" onclick="search()">
          <button type="submit" style="border: none;background: none;cursor: pointer;">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </button>
        </div>
      </form>
      <div id="search-engine">
        <div class="search-engine-header"><strong class="search-engine-title">选择搜索引擎：</strong></div>
        <ul class="search-engine-item">
          <li onclick="selectEngine('{{url}}','{{img}}')">{{_img}}{{name}}</li>
        </ul>
      </div>
    </div>
    <div class="search-navbar">
      <div class="search-navbar-file" style="--text:'清除本地数据'">
        <label onclick="clearDatas()">
          <span>清除本地数据</span>
          <svg t="1747967228486" height="1rem" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path fill="#595959" d="M0 841.264833m66.698336 0l889.920264 0q66.698336 0 66.698336 66.698336l0 49.338495q0 66.698336-66.698336 66.698336l-889.920264 0q-66.698336 0-66.698336-66.698336l0-49.338495q0-66.698336 66.698336-66.698336Z" fill="#333333" p-id="1472"></path><path d="M571.412868 698.000462L798.18721 420.973948a19.187193 19.187193 0 0 0-14.984284-31.430448h-99.590666C683.61226 66.102254 395.987107 8.357941 240.84495 0.683064a18.273517 18.273517 0 0 0-10.050434 36.547033c115.30589 61.947222 166.106267 167.202678 172.136528 332.212534A19.187193 19.187193 0 0 1 383.743851 388.812559h-79.672533a19.187193 19.187193 0 0 0-14.801548 32.161389l226.774342 277.757455a36.547033 36.547033 0 0 0 55.368756-0.730941z" fill="#333333" p-id="1473"></path></svg>
        </label>
      </div>
    </div>
  </div>
  <div class="menu-container">
    <div class="menu-sidebar">
      <div class="menu-sidebar-warp">
        <div class="menu-sidebar-go"><span></span></div>
        <div class="menu-sidebar-content" id="menuSidebar">
          <div class="menu-sidebar-item">
            <a  onclick="sideScroll(event,'1')" class="menu-sidebar-item-a"  href="#sidebar100">未整理</a>
          </div>
        </div>
      </div>
    </div>
    <div class="menu-warp" id="menuWarp">
      <div class="menu-empty-state">
        <div class="menu-empty-icon">📋</div>
        <h3>暂无菜单项</h3>
        <p>点击右上角的导入或添加菜单项</p>
      </div>
    </div>
  </div> <!-- menu-container end -->
  <div class="footer"> - THE END - </div>
  <div class="fixed-bar">
    <div class="fixed-bar-up">
      <a id="scroll-up" href="#">
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 0h24v24H0z" fill="none"></path>
          <path fill="rgba(255,255,255,1)" d="M11.9997 10.8284L7.04996 15.7782L5.63574 14.364L11.9997 8L18.3637 14.364L16.9495 15.7782L11.9997 10.8284Z">
          </path>
        </svg>
      </a>
    </div>
    <div class="fixed-bar-item" onclick="showModal(0)">
      <button>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 24 24" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke="currentColor" height="24" fill="none" class="svg"><line y2="19" y1="5" x2="12" x1="12"></line><line y2="12" y1="12" x2="19" x1="5"></line></svg>
      </button>
    </div>
  </div>
  <div id="toast-container"></div>
  <div class="modal-overlay" id="modal-overlay"></div>
  <div class="modal-container" id="modal-container">
    <div>
      <h3 style="float: left;margin-top: 1rem;" id="modalTitle">菜单项</h3>
      <span class="modal-close" onclick="closeModal()">&times;</span>
    </div>
    <div class="modal-warp">
      <form class="modal-header" id="menuForm" onsubmit="event.preventDefault(); saveEdit();">
        <input type="text" style="display: none;" id="menuId">
        <div class="modal-group" id="iconGroup">
          <input placeholder="‎" type="text" id="icon" required>
          <label for="icon">图标路径</label>
          <div class="error-message">请输入有效的图标路径</div>
        </div>
        <div class="modal-group" id="titleGroup">
          <input placeholder="‎" type="text" id="title" required>
          <label for="title">标题</label>
          <div class="error-message">请输入菜单标题</div>
        </div>
        <div class="modal-group" id="urlGroup">
          <input placeholder="‎" type="text" id="url" required>
          <label for="url">路径</label>
          <div class="error-message">请输入有效的URL路径</div>
        </div>
        <div class="modal-group" id="descGroup">
            <textarea placeholder="‎" id="remark" name="remark" rows="5" ></textarea>
            <label for="remark">内容描述</label>
            <div class="error-message">请输入内容详情</div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="modal-button save-btn">保存</button>
          <button type="button" class="modal-button cancel-btn" onclick="closeModal()">取消</button>
        </div>
      </form>
    </div>
  </div> <!-- modal end -->
</div> <!-- app end -->

</body>

<script type="text/javascript" src="assets/utils/axios.js"></script>
<script type="text/javascript" src="assets/utils/core.js"></script>
<script type="text/javascript" src="assets/door/data/nav.js"></script>
<script type="text/javascript" src="assets/door/data/navMenu.js"></script>
<script type="text/javascript" src="assets/door/data/search.js"></script>
<script type="text/javascript" src="assets/door/model.js"></script>
<script type="text/javascript" src="assets/door/main.js"></script>
<script type="text/javascript" src="assets/door/render.js"></script>
<script type="text/javascript" src="assets/door/scroll.js"></script>
<script type="text/javascript" src="assets/door/form.js"></script>
<script type="text/javascript" src="assets/door/search.js"></script>
<script type="text/javascript" src="assets/door/toast.js"></script>
<script type="text/javascript">
  function to(url){
    window.open(url);
  }
  function toggleEdit(){
    $("menuWarp").classList.toggle('active');
  }
  /* 刷新后返回顶部 */
  window.onbeforeunload = function(){
    document.documentElement.scrollTop = 0
    document.body.scrollTop = 0
  }
  
  document.oncontextmenu = function () { return false };/* 禁止右键 */


</script>
<style>
  body,p,ul,ol,h1,h2,h3,h4,h5 {margin:0;padding: 0;}
  body,div,p,ul,ol,header,footer,section,h1,h2,h3,h4 {box-sizing:border-box;}
  body {
    background-color: #f1f4f9;
    font-family:Tahoma,Verdana,"Microsoft Yahei",sans-serif;
  }
  div {
    -webkit-tap-highlight-color: transparent;
  }
  .footer{
    font-size: 0.7rem;
    text-align: center;
    line-height: 3rem;
    color: #969799;
  }
</style>
</html>

