<!DOCTYPE HTML>
<html>
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/1.png" type="image/x-icon"/>
  <script type="text/javascript" src="assets/vue.min.js"></script>
  <script type="text/javascript" src="assets/data.js"></script>
  <link rel="stylesheet" type="text/css" href="assets/index.css">
  <title>时间线</title>
</head>

<body>
<div id="app">
  <div class="font-div">
    
    <div class="font-div1"><i :class="dengFlag?'iconfont icon-guandeng font-icon':'iconfont icon-kaideng font-icon'" @click="dengMethods()"></i></div>
    <div class="font-div2"><i class="iconfont icon-fanhui font-icon" onClick="javascript :history.back(-1);"></i></div>
  </div>
  <h2 class="top_title">
    <a href="http://yaalaw.gitee.io/share/" :class="dengFlag?'color-white':'color-black'" class="o-header">我 的 网 站</a>
    <br/><span>搭建此网站时间线</span>
  </h2>

  <section id="cd-timeline" class="cd-container">
    <template v-for="list,index in sorttimeLines">
      <div class="cd-timeline-block">
        <div class="cd-timeline-img cd-movie">
          <img src="assets/image/1.png" alt="丢失啦">
        </div>
        <div class="cd-timeline-content">
          <p class="o-title">{{ list.title }} <span class="badge rounded-pill bg-danger" style="font-size: 8px;">{{list.version}}</span></p>
          <hr>
          <p v-html="list.content"></p>
          <span class="cd-date">{{ list.createTime }}</span>
        </div>
      </div>
    </template>
  </section>
</div>
</body>
<script type="text/javascript">
  new Vue({ 
    el: '#app',
    data() {
      return{
        sorttimeLines:sortByKey(data,"createTime"),
        dengFlag:false
      }
    }
  })


  //排序
  function sortByKey(array,key){
    return array.sort(function(a,b){
        var x=a[key];
        var y=b[key];
        return ((x>y)?-1:((x<y)?1:0))
    })
  }
</script>
<style>
  .o-header{
    font-size: 24px;
    font-weight: bold;
  }
  .o-title{
    color: black;
    font-weight: bold;
    font-size: 18px;
    text-align: center;
  }

  .font-icon{
    font-size: 60px;
  }
  .font-icon:hover{
    color:skyblue;
    cursor: pointer;
  }
  .font-div1{
    padding: 20px 0 0 30px;
    float: left;
  }
  .font-div2{
    padding: 20px 30px 0 0;
    float: right;
  }
  .font-div{
    width: 100%;
    height: 60px;
  }
  .body-bg-t{
    background-color: #F2F6FC;
  }
  .body-bg-f{
    background-color: black;
  }
  .color-white{
    color :white;
    text-decoration: none;
  }
  .color-black{
    color: #303133;
    text-decoration: none;
  }

  @media only screen and (max-width: 1170px) {
    .cd-timeline-img img{
      width: 40px;
      height: 40px;
      left: 30%;
      top: 30%;
    }
  }
</style>

</html>