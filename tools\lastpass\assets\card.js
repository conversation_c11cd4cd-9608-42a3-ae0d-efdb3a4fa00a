// 密码卡片渲染与交互（原生JS实现）

// 渲染所有卡片
window.renderPasswordCards = function(passwordList, {
  onCopy, onEdit, onDelete
}) {
  const cardWarper = document.getElementById('cardWarper');
  cardWarper.innerHTML = '';
  if (!passwordList || passwordList.length === 0) {
    document.getElementById('emptyState').style.display = '';
    return;
  } else {
    document.getElementById('emptyState').style.display = 'none';
  }

  passwordList.forEach((item, idx) => {
    const card = document.createElement('div');
    card.className = 'card-item';

    // 状态与类型
    const statusMap = [
        { value: 'normal', label: '正常' },
        { value: 'invalid', label: '失效' },
        { value: 'deleted', label: '删除' },
        { value: 'wrong', label: '密码错误' }
    ]
    const typeMap = {
      database: '数据库', system: '系统', n4a: '4A'
    };
    const statusClass = `status-badge status-${item.status.value || 'invalid'}`;
    const statusText = statusMap[item.status].label || '未知状态';
    const typeText = typeMap[item.type] || item.type;

    card.innerHTML = `
      <div class="card-header">
        <span class="card-type">${typeText}</span>
        <span class="${statusClass}">${statusText}</span>
      </div>
      <div class="card-fields"></div>
      <div class="card-actions">
        <button class="edit-btn">编辑</button>
        <button class="delete-btn danger">删除</button>
      </div>
    `;
    // 填充字段
    const fields = card.querySelector('.card-fields');
    // 系统/数据库/n4a
    fields.innerHTML += `
      <div class="card-field">
        <input value='${item.address || ''}' for="input-field" type="text" disabled>
        <label for="input-field">地址</label>
        <span></span>
        <button class="copy-btn" data-copy="${item.address || ''}">复 制</button>
      </div>
      <div class="card-field">
        <input value='${item.username || ''}' for="input-field" type="text" disabled>
        <label for="input-field">账号</label>
        <span></span>
        <button class="copy-btn" data-copy="${item.username || ''}">复 制</button>
      </div>
      <div class="card-field">
        <input value='••••••••' for="input-field" type="text" disabled>
        <label for="input-field">密码</label>
        <span></span>
        <button class="copy-btn" data-copy="${item.password || ''}">复 制</button>
      </div>
    `;
    if (item.type === 'n4a') {
      fields.innerHTML += `
        <div class="card-field">
          <label>动态口令</label>
          <div class="field-container totp-container">
            <div class="totp-code" id="totp-code-${idx}">-</div>
            <div class="totp-timer" id="totp-timer-${idx}"></div>
            <div class="totp-progress" id="totp-progress-${idx}">
              <div class="totp-progress-bar" style="width: 0%;"></div>
            </div>
          </div>
          <button class="copy-btn" id="totp-copy-${idx}" style="top:-20px;bottom:0">复制</button>
        </div>
      `;
    }
    // 备注
    if (item.remark) {
      fields.innerHTML += `
        <div class="card-field"><label>备注</label>
          <div class="field-container">
            <div class="field-value">${item.remark}</div>
          </div>
        </div>
      `;
    }

    // 事件绑定
    card.querySelectorAll('.copy-btn').forEach(btn => {
      btn.onclick = function() {
        const val = btn.getAttribute('data-copy');
        if (val !== null && onCopy) onCopy(val);
        btn.textContent = '✔ 已复制';
        btn.disabled = true;
        setTimeout(() => {
          btn.textContent = '复制';
          btn.disabled = false;
        }, 1000);
      };
    });
    card.querySelector('.edit-btn').onclick = function() {
      if (onEdit) onEdit(item);
    };
    card.querySelector('.delete-btn').onclick = function() {
      if (onDelete && confirm('确定要删除这条记录吗？')) onDelete(item);
    };

    // TOTP 动态口令
    if (item.type === 'n4a' && item.secret) {
      setupTOTP(idx, item.secret, onCopy);
    }

    cardWarper.appendChild(card);
  });
};

// 动态口令生成（简单演示版，建议用 otpauth.js 或 jsOTP 库）
function generateTOTP(secret) {
  return (Math.floor(Date.now() / 30000) % 1000000).toString().padStart(6, '0');
}

function setupTOTP(idx, secret, onCopy) {
  function update() {
    const code = generateTOTP(secret);
    const remain = 30 - Math.floor((Date.now() / 1000) % 30);
    const codeEl = document.getElementById(`totp-code-${idx}`);
    const timerEl = document.getElementById(`totp-timer-${idx}`);
    // 获取 totp-progress-bar（进度条内层）
    const progressBarEl = document.querySelector(`#totp-progress-${idx} .totp-progress-bar`);
    if (codeEl) codeEl.textContent = code;
    if (timerEl) timerEl.textContent = `${remain}秒后更新`;
    if (progressBarEl) {
      const percent = (remain / 30) * 100;
      progressBarEl.style.width = percent + '%';
    }
  }
  update();
  setInterval(update, 1000);
}