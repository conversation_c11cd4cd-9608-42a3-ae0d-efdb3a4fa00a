
function formatPrice(money) {
  let parts = money.split('.')
  let integerNum = money//String(parts[0]).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  let decimalNum = parts.length>1 ? '.' + parts[1].substr(0,2):'';
  return integerNum + decimalNum
}

function convertCurrency(money){
  let cnNums = new Array('零','壹','贰','叁','肆','伍','陆','柒','捌','玖') //汉字的数字
  let cnIntRadice = new Array('','拾','佰','仟')  //基本单位
  let cnIntUnits = new Array('','万','亿','兆')   //对应整数扩展单位
  let cnDecUnits = new Array('角','分','毫','厘') //对应小鼠部分单位
  let cnInteger = '整'  //整数金额时后面跟的符号
  let cnIntLast = '元'  //整型完一会的单位
  let integerNum; //金额整数部分 最大处理金额 15个9.9999
  let decimalNum; //金额小数部分
  let chineseStr = '' //输出中文金额字符串
  let parts;  // 分离金额后用的数组
  if(money == '') {return '';}
  
  money = parseFloat(money);
  let moneyStr = (''+money).split('.');
  if (money > 999999999999999.9999 || (moneyStr.length>1 && moneyStr[1].length > 4)) {
    throw '超出最大处理数字';
  }
  if (money == 0) {
      chineseStr = cnNums[0] + cnIntLast + cnInteger;
      return chineseStr;
  }

  //转换为字符串
  money = money.toString();
  if (money.indexOf('.') == -1) {
      integerNum = money;
      decimalNum = '';
  } else {
      parts = money.split('.');
      integerNum = parts[0];
      decimalNum = parts[1].substr(0, 4);
  }

  //获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
      var zeroCount = 0;
      var IntLen = integerNum.length;
      for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1);
          var p = IntLen - i - 1;
          var q = p / 4;
          var m = p % 4;
          if (n == '0') {
              zeroCount++;
          } else {
              if (zeroCount > 0) {
                  chineseStr += cnNums[0];
              }
              //归零
              zeroCount = 0;
              chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
          }
          if (m == 0 && zeroCount < 4) {
              chineseStr += cnIntUnits[q];
          }
      }
      chineseStr += cnIntLast;
  }

  //小数部分
  if (decimalNum != '') {
      var decLen = decimalNum.length;
      for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1);
          if (n != '0') {
              chineseStr += cnNums[Number(n)] + cnDecUnits[i];
          }
      }
  }

  if (chineseStr == '') {
      chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum == '') {
      chineseStr += cnInteger;
  }

  return chineseStr;
}