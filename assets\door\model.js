

function Model(){}

// 添加赋值方法,循环赋值
Model.prototype.assign = function(entity){
    if (entity){
        Object.keys(entity).forEach(key => {
            this[key] = entity[key] || this.init[key]; //如果为空则设置默认值
        })
    }
}

// 添加清空方法
Model.prototype.clear = function(){
    Object.keys(this).forEach(key => {
        if (key!=='init' || key!=='toJson'){
            delete this[key]
        }
    })
    Object.keys(this.init).forEach(key => {
        this[key] = this.init[key];
    })
}

// 构造MODEL，传入什么方法就返回什么类型
function InstanceModel(entity){
    let instance = new Model();
    instance.init = entity;
    instance.toJson = function(){
        let result = {}
        Object.keys(entity).forEach(key => {
            result[key] = this[key]
        })
        return result;
    }
    instance.assign(entity);
    return instance;
}

// 构造MODEL
let navModel = InstanceModel(new NavEntity());
let navMenuModel = InstanceModel(new NavMenuEntity());

function NavEntity(){
    this.id = 0;
    this.name = '';
    this.remark = '';
    this.parent_id = 1; // 未分类的
    this.sort = 0;
    this.deleted = 0;
}

// 参数初始化
function NavMenuEntity(){
    this.id = 0;
    this.nav_id = 99; // 未分类的
    this.title = '';
    this.remark = '';
    this.icon = 'assets/icon/zl.png';
    this.url = '';
    this.tags = '';
    this.sort = 0;
    this.deleted = 0;
}





















