// 需要操作到的元素
const root=document.documentElement;
const dropdown_title_icon=document.querySelector(".dropdown-title-icon");
const dropdown_title=document.querySelector(".dropdown-title");
const dropdown_list=document.querySelector(".dropdown-list");
const main_button=document.querySelector(".main-button");
const floating_icon=document.querySelector(".floating-icon");

// 图标对象
const icons={
    "百度":
        "M226.8 535.7c96.8-20.8 83.6-136.4 80.6-161.7-4.8-39-50.6-107.2-112.8-101.8-78.3 7-89.8 120.2-89.8 120.2-10.5 52.3 25.4 164.1 122 143.3z m102.7 201.1c-2.9 8.2-9.1 28.9-3.7 47 10.8 40.6 46.1 42.4 46.1 42.4h50.7V702.4h-54.2c-24.5 7.3-36.3 26.3-38.9 34.4z m76.8-395c53.5 0 96.6-61.5 96.6-137.6 0-76-43.1-137.5-96.6-137.5-53.4 0-96.6 61.5-96.6 137.5 0 76.1 43.2 137.6 96.6 137.6z m230.2 9.1c71.4 9.3 117.4-67 126.4-124.8 9.3-57.6-36.8-124.7-87.3-136.2-50.7-11.6-113.9 69.5-119.7 122.4-6.8 64.8 9.3 129.4 80.6 138.6z m175 339.6S701 605 636.5 512.6c-87.4-136.3-211.7-80.8-253.2-11.6-41.4 69.3-105.8 113.1-115 124.7-9.3 11.5-133.5 78.5-105.9 200.9C189.9 949 286.8 946.8 286.8 946.8s71.3 7 154.2-11.5c82.8-18.4 154.1 4.6 154.1 4.6s193.4 64.7 246.4-60c52.8-124.8-30-189.4-30-189.4z m-331 185.6H354.8c-54.3-10.9-76-47.9-78.7-54.2-2.7-6.4-18.1-36.2-9.9-86.9 23.5-76 90.4-81.4 90.4-81.4h66.9v-82.3l57 0.9v303.9z m234.3-0.9H570c-56.1-14.4-58.7-54.3-58.7-54.3v-160l58.7-1v143.8c3.6 15.3 22.7 18.2 22.7 18.2h59.6v-161h62.5v214.3z m204.8-427.3c0-27.6-22.9-110.9-108.1-110.9-85.3 0-96.7 78.6-96.7 134.1 0 53 4.5 127.1 110.5 124.6 106-2.3 94.3-120 94.3-147.8z m0 0",
    "微信":
        "M308.73856 119.23456C23.65696 170.15296-71.37024 492.23936 155.392 639.66464c12.43392 7.99232 12.43392 7.104-6.21824 62.76096l-15.98464 47.65952 57.43104-30.784 57.43104-30.78656 30.49216 7.40096c31.96928 7.99232 72.82432 13.61664 100.0576 13.61664l16.28416 0-5.62688-21.61152c-44.70016-164.5952 109.82912-327.71072 310.8352-327.71072l27.2384 0-5.62432-19.53792C677.59616 186.43456 491.392 86.67136 308.73856 119.23456zM283.87072 263.40352c30.1952 20.4288 31.97184 64.5376 2.95936 83.48416-47.06816 30.78656-102.1312-23.38816-70.45632-69.57056C230.28736 256.59648 263.74144 249.78688 283.87072 263.40352zM526.62016 263.40352c49.73568 33.45408 12.43392 110.71744-43.22304 89.40288-40.25856-15.39328-44.99712-70.75072-7.40096-90.5856C490.79808 254.22848 513.88928 254.81984 526.62016 263.40352zM636.44928 385.37216c-141.2096 25.7536-239.19872 132.91776-233.57184 256.06656 7.40096 164.89472 200.71168 278.56896 386.32448 227.65312l21.90592-5.92128 46.1824 24.8704c25.4592 13.9136 46.77376 23.97696 47.36512 22.79168 0.59392-1.47968-4.43648-19.24352-10.95168-39.6672-14.79936-45.59104-15.09632-42.33472 4.73856-56.54272C1121.64864 654.464 925.67552 332.97408 636.44928 385.37216zM630.82496 518.28992c12.4288 8.28928 18.944 29.01248 13.61408 44.1088-11.24864 32.26624-59.49952 34.63424-72.52992 3.55328C557.10976 530.13248 597.9648 496.97536 630.82496 518.28992zM828.57472 521.84576c19.53792 18.64704 16.2816 50.32448-6.51264 62.16448-34.93376 17.76128-71.63904-17.76128-53.58336-51.80416C780.32128 510.2976 810.81344 504.97024 828.57472 521.84576z",
    "抖音":
        "M937.4 423.9c-84 0-165.7-27.3-232.9-77.8v352.3c0 179.9-138.6 325.6-309.6 325.6S85.3 878.3 85.3 698.4c0-179.9 138.6-325.6 309.6-325.6 17.1 0 33.7 1.5 49.9 4.3v186.6c-15.5-6.1-32-9.2-48.6-9.2-76.3 0-138.2 65-138.2 145.3 0 80.2 61.9 145.3 138.2 145.3 76.2 0 138.1-65.1 138.1-145.3V0H707c0 134.5 103.7 243.5 231.6 243.5v180.3l-1.2 0.1",
    "哔哩哔哩":
        "M306.005333 117.632L444.330667 256h135.296l138.368-138.325333a42.666667 42.666667 0 0 1 60.373333 60.373333L700.330667 256H789.333333A149.333333 149.333333 0 0 1 938.666667 405.333333v341.333334a149.333333 149.333333 0 0 1-149.333334 149.333333h-554.666666A149.333333 149.333333 0 0 1 85.333333 746.666667v-341.333334A149.333333 149.333333 0 0 1 234.666667 256h88.96L245.632 177.962667a42.666667 42.666667 0 0 1 60.373333-60.373334zM789.333333 341.333333h-554.666666a64 64 0 0 0-63.701334 57.856L170.666667 405.333333v341.333334a64 64 0 0 0 57.856 63.701333L234.666667 810.666667h554.666666a64 64 0 0 0 63.701334-57.856L853.333333 746.666667v-341.333334A64 64 0 0 0 789.333333 341.333333zM341.333333 469.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v85.333333a42.666667 42.666667 0 0 1-85.333333 0v-85.333333a42.666667 42.666667 0 0 1 42.666666-42.666667z m341.333334 0a42.666667 42.666667 0 0 1 42.666666 42.666667v85.333333a42.666667 42.666667 0 0 1-85.333333 0v-85.333333a42.666667 42.666667 0 0 1 42.666667-42.666667z",
    "淘宝":
        "M168.5 273.7a68.7 68.7 0 1 0 137.4 0 68.7 68.7 0 1 0-137.4 0z m730 79.2s-23.7-184.4-426.9-70.1c17.3-30 25.6-49.5 25.6-49.5L396.4 205s-40.6 132.6-113 194.4c0 0 70.1 40.6 69.4 39.4 20.1-20.1 38.2-40.6 53.7-60.4 16.1-7 31.5-13.6 46.7-19.8-18.6 33.5-48.7 83.8-78.8 115.6l42.4 37s28.8-27.7 60.4-61.2h36v61.8H372.9v49.5h140.3v118.5c-1.7 0-3.6 0-5.4-0.2-15.4-0.7-39.5-3.3-49-18.2-11.5-18.1-3-51.5-2.4-71.9h-97l-3.4 1.8s-35.5 159.1 102.3 155.5c129.1 3.6 203-36 238.6-63.1l14.2 52.6 79.6-33.2-53.9-131.9-64.6 20.1 12.1 45.2c-16.6 12.4-35.6 21.7-56.2 28.4V561.3h137.1v-49.5H628.1V450h137.6v-49.5H521.3c17.6-21.4 31.5-41.1 35-53.6l-42.5-11.6c182.8-65.5 284.5-54.2 283.6 53.2v282.8s10.8 97.1-100.4 90.1l-60.2-12.9-14.2 57.1S882.5 880 903.7 680.2c21.3-200-5.2-327.3-5.2-327.3z m-707.4 18.3l-45.4 69.7 83.6 52.1s56 28.5 29.4 81.9C233.8 625.5 112 736.3 112 736.3l109 68.1c75.4-163.7 70.5-142 89.5-200.7 19.5-60.1 23.7-105.9-9.4-139.1-42.4-42.6-47-46.6-110-93.4z"
};

// 下拉列表项
const list_items=["百度","微信","抖音","哔哩哔哩","淘宝"];

// 图标模板
const iconTemplate=(path)=>{
    return `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
            <path d="${path}"></path>
        </svg>
    `;
}

// 下拉列表项模板
const listItemTemplate=(text,translate_value)=>{
    return `
        <li class="dropdown-list-item">
            <button class="dropdown-button list-button" data-translate-value="${translate_value}%">
                <span class="text-truncate">${text}</span>
            </button>
        </li>
    `;
}

// 生成下拉列表项
const renderListItems=()=>{
    dropdown_list.innerHTML+=list_items.map((item,index)=>{
        return listItemTemplate(item,100*index);
    }).join("");
}

// 在页面加载事件中执行生成下拉列表项
window.addEventListener("load",()=>{
    renderListItems();
})

// 设置自定义属性的值
const setDropdownProps=(deg,ht,opacity)=>{
    root.style.setProperty("--rotate-arrow",deg!==0?deg+"deg":0);
    root.style.setProperty("--dropdown-height",ht!==0?ht+"rem":0);
    root.style.setProperty("--list-opacity",opacity);
}

// 下拉框按钮点击事件
main_button.addEventListener("click",()=>{
    const list_wrapper_sizes=3.5;
    const dropdown_open_height=4.6*list_items.length+list_wrapper_sizes;
    const curr_dropdown_height=root.style.getPropertyValue("--dropdown-height")||0;
    curr_dropdown_height==="0"?setDropdownProps(180,dropdown_open_height,1):setDropdownProps(0,0,0);
})

// 下拉列表项鼠标移入事件
dropdown_list.addEventListener("mouseover",(e)=>{
    const translate_value=e.target.dataset.translateValue;
    root.style.setProperty("--translate-value",translate_value);
})

// 下拉列表项点击事件
dropdown_list.addEventListener("click",(e)=>{
    const clicked_item_text=e.target.innerText.toLowerCase().trim();
    const clicked_item_icon=icons[clicked_item_text];
    dropdown_title_icon.innerHTML=iconTemplate(clicked_item_icon);
    dropdown_title.innerHTML=clicked_item_text;
    setDropdownProps(0,0,0);
})

// 下拉列表项鼠标移动事件
dropdown_list.addEventListener("mousemove",(e)=>{
    const icon_size=root.style.getPropertyValue("--floating-icon-size")||0;
    const x=e.clientX-dropdown_list.getBoundingClientRect().x;
    const y=e.clientY-dropdown_list.getBoundingClientRect().y;
    const targetText=e.target.innerText.toLowerCase().trim();
    const hover_item_text=icons[targetText];
    floating_icon.innerHTML=iconTemplate(hover_item_text);
    root.style.setProperty("--floating-icon-left",x-icon_size/2+"px");
    root.style.setProperty("--floating-icon-top",y-icon_size/2+"px");
})