

function transfers(str){
  var result = '';
  for(var i= 0;i<str.length;i++){
      var temp = str.charAt(i);
      var code = temp.charCodeAt();
      if('a' <= temp && temp <= 'z'){
          temp= String.fromCharCode(code-32);
      } else if('A' <= temp && temp <= 'Z'){
          temp= String.fromCharCode(code+32);
      } 
      
      result += temp;
  }
  return result;
}

function wordCount(str){
    var sTotal = 0; // 标点和中文
    var iTotal = 0; // 中文字判断
    var eTotal = 0; // 英文字母
    var inum = 0; // 数字判断
    for (i = 0; i < str.length; i++) {
        var c = str.charAt(i);
        if (c.match(/[\u4e00-\u9fa5]/)) { iTotal++; } //基本汉字
        else if (c.match(/[\u9FA6-\u9fcb]/)){ iTotal++; } //基本汉字补充
    }
    for (i = 0; i < str.length; i++) {
        var c = str.charAt(i);
        if (c.match(/[^\x00-\xff]/)) { sTotal++; }
        else { eTotal++; }
        if (c.match(/[0-9]/)) { inum++; }
    }
    let totalArr = []
    totalArr[0] = inum + iTotal  //字数=inum + iTotal
    totalArr[1] = iTotal * 2 + (sTotal - iTotal)* 2 + eTotal //字符=iTotal * 2 + (sTotal - iTotal)* 2 + eTotal
    totalArr[2] = iTotal //汉字
    totalArr[3] = sTotal-iTotal //标点=sTotal-iTotal
    totalArr[4] = eTotal - inum  //字母=eTotal - inum
    totalArr[5] = inum  //字母=eTotal - inum
    return totalArr
}



