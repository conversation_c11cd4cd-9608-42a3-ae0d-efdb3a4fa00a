<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>多层羊了个羊（遮挡更清晰）</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
    body {
      background: #222;
      color: #fff;
      font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    h1 {
      color: #ffd700;
      margin: 1.2rem 0 0.5rem 0;
      letter-spacing: 2px;
      text-shadow: 2px 2px 0 #000;
    }
    .game-board {
      margin: 0 auto;
      margin-top: 1rem;
      width: 340px;
      height: 340px;
      background: #333;
      border-radius: 18px;
      box-shadow: 0 4px 24px #000a;
      position: relative;
      overflow: visible;
    }
    .tile {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px #0006;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      cursor: pointer;
      user-select: none;
      transition: box-shadow 0.15s, filter 0.2s;
      position: absolute;
      z-index: 1;
      border: 2px solid #fff0;
    }
    .tile.covered {
      filter: grayscale(0.7) brightness(0.7) blur(1px);
      cursor: not-allowed;
      border: 2px solid #8884;
      box-shadow: 0 2px 8px #0002;
    }
    .tile.selected {
      box-shadow: 0 0 0 3px #ffd700, 0 2px 8px #0006;
      z-index: 100 !important;
      border: 2px solid #ffd700;
    }
    .slot-bar {
      margin: 1.5rem auto 0 auto;
      width: 340px;
      min-height: 70px;
      background: #444;
      border-radius: 14px;
      box-shadow: 0 2px 8px #0008;
      display: flex;
      align-items: center;
      padding: 10px 12px;
      gap: 8px;
      flex-wrap: wrap;
      min-height: 70px;
    }
    .slot-tile {
      width: 48px;
      height: 48px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 4px #0006;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      margin-right: 4px;
      margin-bottom: 4px;
      position: relative;
    }
    .slot-tile.match {
      background: #ffd700;
      color: #222;
      animation: pop 0.4s;
    }
    @keyframes pop {
      0% { transform: scale(1);}
      50% { transform: scale(1.2);}
      100% { transform: scale(1);}
    }
    .btn-restart {
      margin: 2rem auto 0 auto;
      display: block;
      background: #ffd700;
      color: #222;
      border: none;
      border-radius: 8px;
      padding: 0.7rem 2.2rem;
      font-size: 1.1rem;
      font-weight: bold;
      cursor: pointer;
      box-shadow: 0 2px 8px #0006;
      transition: background 0.2s;
    }
    .btn-restart:hover {
      background: #ffe066;
    }
    .msg {
      text-align: center;
      font-size: 1.2rem;
      color: #ffd700;
      margin-top: 1.5rem;
      text-shadow: 1px 1px 0 #000;
    }
  </style>
</head>
<body>
  <h1>多层羊了个羊（遮挡更清晰）</h1>
  <div class="game-board" id="gameBoard"></div>
  <div class="slot-bar" id="slotBar"></div>
  <div class="msg" id="msg"></div>
  <button class="btn-restart" id="restartBtn" style="display:none;">重新开始</button>
  <script>
    // 牌面 emoji 占位，可替换为图片
    const tileTypes = [
      {type: 'sheep', emoji: '🐑'},
      {type: 'carrot', emoji: '🥕'},
      {type: 'tree', emoji: '🌲'},
      {type: 'star', emoji: '⭐'},
      {type: 'watermelon', emoji: '🍉'},
      {type: 'flower', emoji: '🌸'},
      {type: 'leaf', emoji: '🍃'}
    ];
    const PAIRS = 3; // 三消
    const TOTAL_TYPES = tileTypes.length;
    // 第一层最多，后面逐层减少
    const LAYER_CONFIG = [
      {rows: 4, cols: 5}, // 第一层 20
      {rows: 3, cols: 4}, // 第二层 12
      {rows: 2, cols: 3}  // 第三层 6
    ];
    const SLOT_LIMIT = 7;

    let tiles = [];
    let slot = [];
    let finished = false;

    function shuffle(arr) {
      for (let i = arr.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [arr[i], arr[j]] = [arr[j], arr[i]];
      }
      return arr;
    }

    function genTiles() {
      // 生成多层叠放的牌组
      let allTiles = [];
      let pool = [];
      // 计算总牌数
      let totalTiles = LAYER_CONFIG.reduce((sum, l) => sum + l.rows * l.cols, 0);
      // 保证每种3张
      for (let i = 0; i < Math.floor(totalTiles / PAIRS); i++) {
        for (let j = 0; j < PAIRS; j++) {
          pool.push(tileTypes[i % TOTAL_TYPES]);
        }
      }
      shuffle(pool);

      let idx = 0;
      let boardW = 340, boardH = 340;
      for (let l = 0; l < LAYER_CONFIG.length; l++) {
        const {rows, cols} = LAYER_CONFIG[l];
        // 层的偏移让遮挡更明显
        let offsetX = 20 + l * 20;
        let offsetY = 20 + l * 20;
        let cellW = (boardW - 2 * offsetX) / cols;
        let cellH = (boardH - 2 * offsetY) / rows;
        for (let r = 0; r < rows; r++) {
          for (let c = 0; c < cols; c++) {
            if (idx >= pool.length) continue;
            allTiles.push({
              ...pool[idx],
              id: l + '-' + r + '-' + c + '-' + Math.random().toString(36).slice(2),
              removed: false,
              layer: l,
              row: r,
              col: c,
              x: offsetX + c * cellW,
              y: offsetY + r * cellH,
              covered: false
            });
            idx++;
          }
        }
      }
      return allTiles;
    }

    function updateCovered() {
      // 标记被覆盖的牌
      for (let i = 0; i < tiles.length; i++) {
        let t = tiles[i];
        if (t.removed) {
          t.covered = false;
          continue;
        }
        // 被上面一层的同位置或相邻位置覆盖
        t.covered = tiles.some(o =>
          !o.removed &&
          o.layer > t.layer &&
          Math.abs(o.x - t.x) < 60 &&
          Math.abs(o.y - t.y) < 60
        );
      }
    }

    function initGame() {
      tiles = genTiles();
      slot = [];
      finished = false;
      document.getElementById('msg').textContent = '';
      document.getElementById('restartBtn').style.display = 'none';
      updateCovered();
      renderBoard();
      renderSlot();
    }

    function renderBoard() {
      const board = document.getElementById('gameBoard');
      board.innerHTML = '';
      // 按层排序，保证上层在上面
      tiles
        .filter(tile => !tile.removed)
        .sort((a, b) => a.layer - b.layer)
        .forEach((tile, idx) => {
          const div = document.createElement('div');
          div.className = 'tile' + (tile.covered ? ' covered' : '');
          div.textContent = tile.emoji;
          div.style.left = tile.x + 'px';
          div.style.top = tile.y + 'px';
          div.style.zIndex = tile.layer * 10 + idx;
          div.onclick = () => onTileClick(tile.id);
          board.appendChild(div);
        });
    }

    function renderSlot(matchIdxs = []) {
      const slotBar = document.getElementById('slotBar');
      slotBar.innerHTML = '';
      slot.forEach((tile, idx) => {
        const div = document.createElement('div');
        div.className = 'slot-tile' + (matchIdxs.includes(idx) ? ' match' : '');
        div.textContent = tile.emoji;
        slotBar.appendChild(div);
      });
    }

    function onTileClick(tileId) {
      if (finished) return;
      const tile = tiles.find(t => t.id === tileId);
      if (tile.removed || tile.covered) return;
      // 放入槽
      slot.push(tile);
      tile.removed = true;
      updateCovered();
      renderBoard();

      // 检查三消
      let matchType = tile.type;
      let matchIdxs = [];
      for (let i = 0; i < slot.length; i++) {
        if (slot[i].type === matchType) matchIdxs.push(i);
      }
      if (matchIdxs.length === PAIRS) {
        setTimeout(() => {
          renderSlot(matchIdxs);
          setTimeout(() => {
            slot = slot.filter((t, i) => !matchIdxs.includes(i));
            renderSlot();
            checkWin();
          }, 350);
        }, 10);
      } else {
        renderSlot();
        checkLose();
      }
    }

    function checkWin() {
      if (tiles.every(t => t.removed) && slot.length === 0) {
        finished = true;
        document.getElementById('msg').textContent = '🎉 恭喜通关！';
        document.getElementById('restartBtn').style.display = 'block';
      }
    }

    function checkLose() {
      if (slot.length > SLOT_LIMIT) {
        finished = true;
        document.getElementById('msg').textContent = '😢 失败，槽已满！';
        document.getElementById('restartBtn').style.display = 'block';
      }
    }

    document.getElementById('restartBtn').onclick = initGame;

    initGame();
  </script>
</body>
</html>