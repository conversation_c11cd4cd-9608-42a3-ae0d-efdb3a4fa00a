
.form-container{
    width: 96%;
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-warper{
    margin: 0 auto;
    width: 90%;
    display: flex;
    flex-direction: column;
    padding: 20px 0;
}

.form-group {
    position: relative;
    margin-bottom: 20px;
}

.form-group > label {
  font-size: 14px;
  color: rgb(99, 102, 102);
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: #fff;
  transition: all .3s ease;
}


.form-group input,
.form-group textarea {
    padding: 10px 0;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    outline: 0;
    width: 100%;
    text-indent: 10px;
    background-color: transparent;
}

.form-group input:placeholder-shown+ label, .form-group textarea:placeholder-shown +label {
  top: 10px;
  background-color: transparent;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #3366cc;
}

.form-group input:focus+ label, .form-group textarea:focus +label {
  top: -10px;
  left: 10px;
  background-color: #fff;
  color: #3366cc;
  font-weight: 600;
  font-size: 14px;
}

.form-group textarea {
  resize: none;
  height: 100px;
}

.form-actions{
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 10px;
}
.form-actions button {
    background-color: #3366cc;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 10px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-actions button:hover {
  background-color: #27408b;
}



