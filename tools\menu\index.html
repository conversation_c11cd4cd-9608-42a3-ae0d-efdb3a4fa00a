<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8" />
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" /><!-- 兼容标签 -->
  <link rel="shortcut icon" href="../../door/assets/icon/logo.png" type="image/x-icon" />
  <title>工具菜单</title>
</head>
<body>
<div id="app">
  <div class="card-container">
    <div class="card-warper" id="cardWarper"></div>
  </div>
</div>
</body>
<script>
  // 默认SVG图标
  const defaultSvg = `
    <svg width="24" height="24" viewBox="0 0 48 48" fill="none">
      <rect x="6" y="6" width="36" height="36" rx="10" fill="#ffd700" stroke="#222" stroke-width="3"/>
      <circle cx="24" cy="24" r="10" fill="#fff" stroke="#222" stroke-width="2"/>
      <circle cx="24" cy="24" r="5" fill="#ffd700" stroke="#222" stroke-width="1.5"/>
    </svg>
  `;

  // 高效渲染菜单
  function renderMenu(data) {
    const grid = document.getElementById('cardWarper');
    let html = '';
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      // 优先用data里的svg字段，没有则用默认svg
      const iconSvg = item.svg ? item.svg : defaultSvg;
      html += `
        <div class="card-item" onclick="window.location.href='../${item.url}/index.html'">
          <div class="card">
            <div class="card-icon">${iconSvg}</div>
            <div class="card-title">${item.title}</div>
          </div>
        </div>
      `;
    }
    grid.innerHTML = html;
  }

  // 动态加载 assets/data.js 并渲染
  (function(){
    const script = document.createElement('script');
    script.src = 'assets/data.js';
    script.onload = function() {
      if (window.menuData && Array.isArray(window.menuData)) {
        renderMenu(window.menuData);
      } else {
        document.getElementById('cardWarper').innerHTML = '<div style="color:#ffd700;text-align:center;">未找到菜单数据</div>';
      }
    };
    script.onerror = function() {
      document.getElementById('cardWarper').innerHTML = '<div style="color:#ffd700;text-align:center;">菜单数据加载失败</div>';
    };
    document.body.appendChild(script);
  })();
</script>
<style>
  html, body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    box-sizing: border-box;
  }
  .card-container {
    width: 98%;
    max-width: 1200px;
    margin: 0 auto;
  }
  .card-warper{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(5rem, 1fr)); /* 每列最小220px，自动适应 */
    gap: 1.2rem;
    justify-content: center;
    width: 100%;
    padding: 1.5rem 0;
  }
  .card-item{
    text-align: center;
    border-radius: 6px;
    cursor: pointer;
    background: rgba(255,241,240,0.3); /* #fff1f0 更透明 */
    overflow: hidden;
    transition: all 0.18s ease;
  }
  .card-item:hover {
    transform: translateY(-4px) scale(1.03);
  }
  .card {
    aspect-ratio: 1 / 1;           /* 保持正方形 */
    border-radius: 12px;
    background: transparent;
    transition: background 0.18s;
    position: relative;
  }
  .card-icon {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.18s;
  }
  .card-icon > svg{
    width: 50%;
  }
  .card-title {
    color: white;
    font-size: 12px;
    position: absolute;
    bottom: 0px;
    width: 100%;
    background: rgba(0,0,0,0.3);
    padding: 0.3rem 0;
  }

</style>
</html>