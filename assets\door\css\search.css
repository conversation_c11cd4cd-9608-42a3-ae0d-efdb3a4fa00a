
.search-container{
    margin: 1rem 10px;
}

.search-content{
    width: 86%;
    max-width: 600px;
    margin: 3rem auto 2rem auto;
}

.search-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    background-color: #fff;
    border-radius: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 2px solid transparent;
    overflow: hidden;
    height: 3rem;
    margin-bottom: 10px;
  }
  
  .search-wrapper:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
  
  .search-wrapper:focus-within {
    border-color: #4a90e2;
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.2);
  }
  
#search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    color: #666;
    transition: color 0.3s ease;
    cursor: pointer;
}
  
.search-wrapper:focus-within #search-icon {
    color: #4a90e2;
}
  
.search-box {
    flex: 1;
    padding: 15px 0;
    border: none;
    font-size: 16px;
    background-color: transparent;
    color: #333;
    width: 100%;
}
  
.search-box:focus {
    outline: none;
}
  
.search-clear,.search-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    color: #999;
    cursor: pointer;
    transform: scale(0.8);
    transition: all 0.3s ease;
}
.search-clear{
    opacity: 0;
} 
.search-clear:hover {
    color: #f44336;
}
  
.search-wrapper:focus-within .search-clear,
.search-box:not(:placeholder-shown) ~ .search-clear {
    opacity: 1;
    transform: scale(1);
}

#search-engine {
    position:absolute;
    width: 86%;
    max-width: 600px;
    border-radius: 10px;
    background:#FFF;
    box-shadow:0px 5px 20px 0px #d8d7d7;
    transition:all 0.3s;
    display:none;
    z-index:999;
    left:50%;
    transform: translate(-50%,0);
    padding: 1rem;
}
.search-engine-header {
    margin-bottom:10px;
}
.search-engine-title {
    font-size:14px;
    color:#999;
}
.search-engine-header::after {
    content: '';
    width: 70px;
    height: 18px;
    position: absolute;
    top: -17px;
    left: 1px;
}
.search-engine-header::before {
    content:'';
    width:0px;
    height:0px;
    position:absolute;
    top:-15px;
    left:20px;
    border-top:8px solid transparent;
    border-right:8px solid transparent;
    border-bottom:8px solid #fff;
    border-left:8px solid transparent;
}

.search-engine-item {
    display: flex;
    flex-wrap: wrap;
}


.search-engine-item > li{
    display: flex;
    align-items: center; /* 垂直居中 */
    height: 30px;
    text-align:left;
    font-size:14px;
    padding:3px 6px;
    margin:0 10px 10px 0;
    background:#f9f9f9;
    color:#999;
    cursor:pointer;
    list-style:none;
    border-radius:6px;
}
.search-engine-item > li > img{
    width:25px;
    height:25px;
    margin-right: 3px;
}

.search-footer{
    text-align: right;
    color:#929292;
    font-size: 12px;
}



.search-navbar{
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-bottom: 10px;
}

.search-navbar > div{
    padding: 0 10px;
    border-left: 1px solid #d9d9d9;
}

.search-navbar > div:first-child{
    border-left: none;
}

.search-navbar-file > label {
    padding: 0;
    margin: 0;
    border: none;
    background: none;
    cursor: pointer;
  }
  
  .search-navbar-file >  label {
    --primary-color: #595959;
    --hovered-color: #000;
    position: relative;
    display: flex;
    font-weight: 600;
    gap: 0.3rem;
    align-items: center;
  }
  
  .search-navbar-file >  label span {
    margin: 0;
    position: relative;
    font-size: 14px;
    color: var(--primary-color);
  }
  
  .search-navbar-file >   label::after {
    position: absolute;
    content: "";
    width: 0;
    left: 0;
    bottom: -7px;
    background: var(--hovered-color);
    height: 2px;
    transition: 0.3s ease-out;
  }
  
  .search-navbar-file > label span::before {
    position: absolute;
    /*   box-sizing: border-box; */
    content: var(--text);
    width: 0%;
    inset: 0;
    color: var(--hovered-color);
    overflow: hidden;
    transition: 0.3s ease-out;
  }
  
  .search-navbar-file >  label:hover::after {
    width: 100%;
  }
  
  .search-navbar-file >  label:hover span::before {
    width: 100%;
  }
  
  .search-navbar-file >  label:hover svg {
    transform: translateX(2px);
    fill: #000;
  }
  
  .search-navbar-file >  label svg {
    color: var(--primary-color);
    transition: 0.2s;
    position: relative;
    width: 1.25rem;
    transition-delay: 0.2s;
  }
  

