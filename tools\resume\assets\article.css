
.article-backcolor{
  background-color: white;
  border-radius: 5px;
  padding: 10px;
  margin: 3px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1)
}

.article-content-text>p{
  font-size: 1rem;
  margin-bottom: 6px;
  text-indent: 1rem;
}

.article-tag{
  transform: skewX(-30deg);
  background-color: #4A4E59;
  margin: 10px 0px;
  border-radius: 5px;
}

.article-tag-title{
  transform: skewX(30deg);
  color: #69c0ff;
  line-height: 2rem;
}

.article-tag-title > span:nth-child(2){
  color:white;
}


.article-tag-title>span{
  margin-left: 1rem;
}

.article-content-title{
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 6px;
}
@media screen and (min-width: 600px) {
  .article{
    margin-left: 220px;
  }

  .article>div{
    width: 90%;
    margin-left: 1rem;
  }

  .article-title{
    font-weight: bolder;
    font-size: 1.5rem;
    text-align: right;
    line-height: 2rem;
    color: #4A4E59;
  }
}
@media screen and (max-width: 600px) {
  .article{
    width: 90%;
    margin: 1rem auto;
  }

  .article-title{
    display: none;
  }
}