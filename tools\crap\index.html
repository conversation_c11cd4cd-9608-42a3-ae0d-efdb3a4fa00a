<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/1.jpeg" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/style.css">
  <title>废话文学</title>
</head>
<body>
<div id="app">
  <div class="warp">
    <div><h3 class="nav-title">聊天用词</h3></div>
  <div id="replyDome" style="display: none;"><span class="reply-content" style="--i: {{index}}">{{content}}</span></div>
    <div id="reply">
      <div class="reply-title" style="--i: {{index}}">{{title}}</div>
    </div>
  </div>
  <div class="warp">
    <h3 class="nav-title" onclick="ling()">长句语录</h3>
    <div id="sen">
      <div class="sen-content">{{content}}</div>
    </div>
  </div>
</div>
</body>

<script src="assets/core.js"></script>
<script src="assets/sen.js"></script>
<script src="assets/reply.js"></script>
<script src="assets/ling.js"></script>
<script>

var data1 = reply

function init() {
  let senInnerHtml = $('#sen').innerHTML;
  let senHtmlArr = [];
  sen.forEach(element => {
    let _html = senInnerHtml.replace('{{content}}', element.content)
    senHtmlArr.push(_html);
  })
  $('#sen').innerHTML = senHtmlArr.join('');

  let relpyInnerHtml = $('#reply').innerHTML;
  let relpyHtmlArr = [];
  let relpyArr = new Array();
  let replyDome = $('#replyDome').innerHTML;
  console.log(replyDome)
  data1.forEach(element => {
    let _html = relpyInnerHtml.replace('{{title}}', element.title).replace('{{index}}', Math.random()*18)
    let contentArr = element.content.split("/")
    for(i = 0,len=contentArr.length; i < len; i++) {
      _html = _html + replyDome
        .replace('{{content}}', contentArr[i])
        .replace('{{index}}', Math.random()*18)
    }
    relpyHtmlArr.push(_html);
  })
  
  $('#reply').innerHTML = relpyHtmlArr.join('');
}
init()


</script>

</html>