<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/wx.jpg" type="image/x-icon"/>
  <link rel="stylesheet" type="text/css" href="assets/aside.css">
  <link rel="stylesheet" type="text/css" href="assets/article.css">
  <title>个人简历</title>
</head>
<body>
  <div class="container-full" id="app">
    <div class="aside">
      <h3 class="aside-title">PERSONAL RESUME</h3>
      <div class="aside-name">谢 涛</div>
      <div class="aside-tag">
        <div class="aside-tag-targon"></div>
        <div class="aside-tag-title">基本信息</div>
      </div>
      <div class="aside-info">
        <p>出生年月：1995.04</p>
        <p>工作经验：3年</p>
        <p>现居：广州</p>
        <p>电话：17602022639</p>
        <p>邮箱：<EMAIL></p>
        <p>Gitee：<a href="https://gitee.com/yaalaw" style="color: white;">gitee.com/yaalaw</a></p>
      </div>
      
      <div class="aside-tag">
        <div class="aside-tag-targon"></div>
        <div class="aside-tag-title">求职意向</div>
      </div>
      <div class="aside-info">
        <p>求职意向：Java开发工程师</p>
        <p>期望薪资：面议</p>
      </div>
    </div>

    <div class="article">
      <div class="article-title">PERSONAL RESUME</div>
      <div class="article-tag">
        <div class="article-tag-title"><span>教育背景</span><span>Education</span></div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-title">
          <span>2014.9—2017.6</span>
          <span style="margin-left: 2rem;">湖南电子科技职业学院</span>
          <span style="margin-left: 2rem;">软件技术（专科）</span>
        </div>
      </div>
      <div class="article-tag">
        <div class="article-tag-title"><span>工作经历</span><span>Experience</span></div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-title">
          <span>2019.05-至今</span>
          <span style="margin-left: 2rem;">广州市勤思网络科技有限公司</span>
          <span style="margin-left: 2rem;">保险事业部</span>
        </div>
        <div class="article-content-text">
          <p>✧ 在项目中参与需求的讨论与关系图</p>
          <p>✧ 根据需求设计出最优的开发方案</p>
          <p>✧ 项目的需求文档与详细文档编写</p>
          <p>✧ 根据文档实现数据开发与代码编写</p>
          <p>✧ 项目的运维与后期问题修复</p>
        </div>
      </div>
      <div class="article-tag">
        <div class="article-tag-title"><span>职业技能</span><span>Akills</span></div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-text">
          <p>1. Java SE/EE基础知识扎实，具有良好的数据结构基础</p>
          <p>2. 熟悉设计模式能熟练使用常用的设计模式进行开发</p>
          <p>3. 熟悉 Spring，SpringMVC，Springboot，Mybatis，Redis</p>
          <p>4. 熟悉 Mysql、SQL Server Oracle，数据库表设计，了解sql优化，数据调优，数据备份等</p>
          <p>5. 了解微服务 SpringCloud 技术生态，了解Ribbon、OpenFeign、Hystrix</p>
          <p>6. 了解 RabbitMQ 等中间件</p>
          <p>7. 了解 html、css、JavaScript、jQuery、ajax、vue2/3、bootstrap、微信小程序等技术</p>
          <p>8. 熟悉 IDEA，Git，maven 等开发工具</p>
          <p>9. 具有良好的面向对象思维和编码习惯，善于重构代码和有 CodeReview 习惯</p>
          <p>10. 利用业余时间学习了vue3</p>
        </div>
      </div>
      <div class="article-tag">
        <div class="article-tag-title"><span>自我评价</span><span>Evaluation</span></div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-text">
          <p>1. 乐于学习新技术并且进行使用</p>
          <p>2. 工作认真细致负责，力求编写可维护可易于阅读的代码</p>
        </div>
      </div>
      <div class="article-tag">
        <div class="article-tag-title"><span>项目经验</span><span>Project Experience</span></div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-title">
          <span>➢ 广东省分公司佣金集中计算系统</span>
          <span style="margin-left: 2rem;">2020.05-至今</span>
        </div>
        <div class="article-content-title"><span>项目介绍</span></div>
        <div class="article-content-text">
          <p>
            该项目是中国人寿广东省省公司主推的佣金集中管理系统，将广东省省内各地市的营销员佣金做统一的标准化的管理，
            减少了各个子公司因方案非标准化，奖项数量多，方案制定人数众多，
            奖项指标设计随意，数据来源依靠人工，给佣金计算工作带来巨大压力的问题
          </p>
          <p>1. 广东省分公司佣金集中计算系统是从专项方案管理信息系统分离出来的系统</p>
          <p>2. 主要模块有：兑现管理、兑现补调整、新增作废规则管理、回退管理、工单管理、权限管理等模块</p>
          <p>3. 项目兼容个险、收展、银保、团险的佣金管理</p>
        </div>
        <div class="article-content-title"><span>项目职责</span></div>
        <div class="article-content-text">
          <p>1. 负责项目的新需求的开发和调整</p>
          <p>2. 负责项目的性能优化和数据报表的开发</p>
          <p>3. 配合测试人员对系统进行维护</p>
        </div>
        <div class="article-content-title"><span>技术描述</span></div>
        <div class="article-content-text">
          <p><span style="font-weight:bold">∎ 开发工具：</span>IDEA、Maven、Git</p>
          <p><span style="font-weight:bold">∎ 操作系统：</span>Window</p>
          <p><span style="font-weight:bold">∎ 数据库：</span>SQL Server</p>
          <p><span style="font-weight:bold">∎ 使用技术：</span>Spring、SpringMVC、Mybatis、Springboot、Redis</p>
        </div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-title">
          <span>➢ 广东省分公司佣金集中计算系统-数据开发</span>
          <span style="margin-left: 2rem;">2020.03-至今</span>
        </div>
        <div class="article-content-title"><span>项目介绍</span></div>
        <div class="article-content-text">
          <p>
            将经办在方案系统中录入的方案进行数据计算加工，加工后返回计算结果和计算过程明细表。
          </p>
          <p>1. 主要分为增员人数、持证人力数、有效人力数、FYC、主险举绩状态、累计保费、保单件数、每满奖励、参会率、考核状态等等指标类型</p>
        </div>
        <div class="article-content-title"><span>项目职责</span></div>
        <div class="article-content-text">
          <p>1. 负责指标计算的需求讨论与数据开发架构流程设计</p>
          <p>2. 负责指标计算开发、数据优化、数据安全监测等扩展功能如日志、灾备处理</p>
          <p>3. 对易出现问题的部分，进行更多的安全检测</p>
          <p>4. 配合测试人员对数据的准确性，安全性进行优化修复</p>
        </div>
        <div class="article-content-title"><span>使用工具</span></div>
        <div class="article-content-text">
          <p><span style="font-weight:bold">∎ BCompare：</span>文件对比工具，因跟钱直接挂钩所以安全性</p>
          <p><span style="font-weight:bold">∎ Citrix Receiver：</span>云桌面工具，只要有电脑在哪里都能处理问题</p>
          <p><span style="font-weight:bold">∎ SSMS：</span>SQL Server的数据管理工具</p>
        </div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-title">
          <span>➢ 职场管理系统</span>
          <span style="margin-left: 2rem;">2019.05-2019-09</span>
        </div>
        <div class="article-content-title"><span>项目介绍</span></div>
        <div class="article-content-text">
          <p>
            省公司为了更好的管理各个职场的使用情况与坐班情况
          </p>
          <p>1. 主要模块分为</p>
        </div>
        <div class="article-content-title"><span>项目职责</span></div>
        <div class="article-content-text">
          <p>1. 负责新需求的开发与修改</p>
          <p>2. 负责项目的维护与性能优化</p>
        </div>
        <div class="article-content-title"><span>使用工具</span></div>
        <div class="article-content-text">
          <p><span style="font-weight:bold">∎ BCompare：</span>文件对比工具，因跟钱直接挂钩所以安全性</p>
          <p><span style="font-weight:bold">∎ Citrix Receiver：</span>云桌面工具，只要有电脑在哪里都能处理问题</p>
          <p><span style="font-weight:bold">∎ SSMS：</span>SQL Server的数据管理工具</p>
        </div>
      </div>
      <div class="article-tag">
        <div class="article-tag-title"><span>兴趣爱好</span><span>Hobby</span></div>
      </div>
      <div class="article-backcolor">
        <div class="article-content-text">
          <p>音乐、羽毛球、乒乓球、旅游、游戏</p>
        </div>
      </div>
      <div class="fonter">
        <a href="http://yaalaw.gitee.io/share/" class="myhome">我的首页</a>
        <span class="fonter-font" @click="showCallMe=!showCallMe">
          联系我
          <i class="iconfont icon-erweima" style="font-size: 13px;"></i>
        </span>
        <div v-if="showCallMe" class="wx-box">
          <image class="wx-box-img" src="assets/image/wx.jpg"></image>
        </div>
        <span class="fonter-font">关于</span>
      </div>
    </div>
  </div>
</body>
<script>
  var el = document.getElementById("app").style.height = document.documentElement.clientHeight + 'px'
</script>
<style>
  body{
    background-color: #f0f0f0;
    margin: 0px;
  }
  .container{
    width: 100%;
  }
  .fonter{
    text-align: center;
    line-height: 60px;
    font-size: 13px;
    color: #909399;
    position: relative;
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }
  .myhome{
    text-decoration: none;
    color: #909399;
  }
  .fonter-font{
    padding-left: 10px;
    margin-left: 5px;
    border-left:1px solid #909399;
    cursor: pointer;
  }
  .wx-box{
    position: absolute;
    top: -200px;
    z-index: 99;
    border-radius: 10px;
    width: 100%;
  }
  .wx-box-img{
    width: 200px;
    height: 200px;
    margin: auto;
  }
</style>
</html>