


/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  cursor: pointer;
  display: none;
}

/* 提示框样式 */
.toast-container {
  position: fixed;
  top: var(--header-height);
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  pointer-events: none;
}

.toast {
  background-color: var(--card-background);
  color: var(--text-color);
  padding: 12px 24px;
  border-radius: var(--border-radius);
  margin-top: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  animation: slideDown 0.3s ease, fadeOut 0.3s ease 2.7s;
  border: 1px solid #eee;
  pointer-events: auto;
}

@keyframes slideDown {
  from {
      transform: translateY(-100%);
      opacity: 0;
  }
  to {
      transform: translateY(0);
      opacity: 1;
  }
}

@keyframes fadeOut {
  from {
      transform: translateX(0);
      opacity: 1;
  }
  to {
      transform: translateX(100%);
      opacity: 0;
  }
}


/* 关闭按钮样式 */
.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: var(--transition);
  min-width: unset;
  z-index: 2;
}

.modal-close:hover {
  color: var(--danger-color);
  background-color: rgba(0,0,0,0.05);
  transform: none;
  box-shadow: none;
}

.modal-close::before,
.modal-close::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 2px;
  background-color: currentColor;
  transform-origin: center;
}

.modal-close::before {
  transform: rotate(45deg);
}

.modal-close::after {
  transform: rotate(-45deg);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .modal-content {
      width: calc(100% - 32px);
      margin: 16px;
      padding: 20px;
  }
  
  .modal-close {
      top: 16px;
      right: 16px;
  }
} 








