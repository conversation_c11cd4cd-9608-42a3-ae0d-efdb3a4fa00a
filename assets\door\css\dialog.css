
  /* 确认对话框样式 */
  .confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
  }

  .confirm-dialog.active {
    opacity: 1;
    visibility: visible;
  }

  .confirm-dialog-content {
    background-color: white;
    border-radius: 16px;
    padding: 25px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-align: center;
  }

  .confirm-dialog.active .confirm-dialog-content {
    transform: scale(1);
  }

  .confirm-dialog h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 1.5rem;
  }

  .confirm-dialog p {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.5;
  }

  .confirm-dialog-buttons {
    display: flex;
    justify-content: space-between;
  }

  .confirm-dialog-buttons button {
    width: 48%;
    padding: 12px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .confirm-dialog-buttons .confirm-btn {
    background-color: #f44336;
    color: white;
    box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3);
  }

  .confirm-dialog-buttons .confirm-btn:hover {
    background-color: #d32f2f;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(244, 67, 54, 0.4);
  }

  .confirm-dialog-buttons .cancel-btn {
    background-color: #f0f0f0;
    color: #666;
  }

  .confirm-dialog-buttons .cancel-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  }