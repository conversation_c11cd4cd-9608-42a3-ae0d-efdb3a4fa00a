// 密码本主逻辑（原生JS实现）

// 工具函数
function $(id) { return document.getElementById(id); }
function isEmpty(value) {
  if (value == null) return true;
  if (value === '') return true;
  if (Array.isArray(value) && value.length === 0) return true;
  if (typeof value === 'object' && Object.keys(value).length === 0) return true;
  return false;
}

// 数据初始化
let lastPass = [];
if (!isEmpty(localStorage.getItem("lastpass"))) {
  lastPass = JSON.parse(localStorage.getItem("lastpass"));
}

// 过滤条件
let searchFilter = "";
let keywordFilter = "";

// 渲染卡片
function refreshCards() {
  let filtered = lastPass.filter(item => {
    if (item.status == 4) return false; // 已删除
    if (searchFilter && item.type !== searchFilter) return false;
    if (keywordFilter && !(item.address?.includes(keywordFilter) || item.username?.includes(keywordFilter))) return false;
    return true;
  });
  window.renderPasswordCards(filtered, {
    onCopy: val => {
      try {
        const temp = document.createElement('input');
        temp.value = val;
        document.body.appendChild(temp);
        temp.select();
        document.execCommand('copy');
        document.body.removeChild(temp);
        showToast('已复制');
      } catch (e) {
        showToast('复制失败');
      }
    },
    onEdit: item => {
      window.openPasswordForm(item, saveItem);
    },
    onDelete: item => {
      item.status = 4;
      saveData();
      refreshCards();
      showToast('已删除');
    }
  });
}

// 保存数据
function saveData() {
  localStorage.setItem("lastpass", JSON.stringify(lastPass));
}

// 新增
$("addBtn").onclick = function() {
  window.openPasswordForm({}, saveItem);
};

// 保存/编辑回调
function saveItem(data) {
  if (data.id) {
    // 编辑
    let idx = lastPass.findIndex(x => x.id === data.id);
    if (idx !== -1) lastPass[idx] = data;
  } else {
    // 新增
    data.id = Date.now().toString();
    lastPass.push(data);
  }
  saveData();
  refreshCards();
  showToast('保存成功');
}

// 搜索
$("searchQuery").addEventListener('input', function() {
  keywordFilter = this.value.trim();
  refreshCards();
});

// 类型筛选
$("searchFilter").addEventListener('change', function() {
  searchFilter = this.value;
  refreshCards();
});

// 导出
$("exportBtn").onclick = function() {
  try {
    let data = JSON.stringify(lastPass, null, 2);
    let blob = new Blob([data], { type: 'application/json' });
    let a = document.createElement('a');
    a.download = 'lastPass_' + new Date().toISOString().slice(0, 10) + '.json';
    a.href = URL.createObjectURL(blob);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(a.href);
    showToast('导出成功');
  } catch (e) {
    showToast('导出失败');
  }
};

// 导入
$("importBtn").onclick = function() {
  $("importFile").click();
};
$("importFile").onchange = function(e) {
  let file = e.target.files[0];
  if (!file) return;
  let reader = new FileReader();
  reader.onload = function(ev) {
    try {
      let imported = JSON.parse(ev.target.result);
      if (Array.isArray(imported)) {
        if (confirm('确定要导入新的配置吗？这将覆盖现有配置。')) {
          lastPass = imported;
          saveData();
          refreshCards();
          showToast('导入成功');
        }
      } else {
        showToast('导入失败：文件格式不正确');
      }
    } catch (e) {
      showToast('导入失败');
    }
  };
  reader.readAsText(file);
  e.target.value = '';
};

// Toast 提示
function showToast(msg) {
  const toast = document.createElement('div');
  toast.className = 'toast';
  toast.innerText = msg;
  $("toastContainer").appendChild(toast);
  setTimeout(() => {
    toast.remove();
  }, 1500);
}

// 初始化
window.onload = function() {
  refreshCards();
};