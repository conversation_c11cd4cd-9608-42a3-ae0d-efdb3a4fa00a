  /* 禁止选择文字 */
  div{
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }
  /*手机点击有红框，去红框*/
  a,button,input{
    -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
    -moz-tap-highlight-color:rgba(0, 0, 0, 0);
    -ms-tap-highlight-color:rgba(0, 0, 0, 0);
    -o-tap-highlight-color:rgba(0, 0, 0, 0);/*解决移动端点击显示背景框问题*/
  }


  body{
    margin: 0px;
  }

  .container{
    width: 100%;
    height: 100%;
    display:flex;
    justify-content:center;
    align-items:center;
  }

  .context{
    text-align: center;
  }

  .title>img{
    position: absolute;
    z-index: -1;
    width: 13%;
    max-width: 13%;
    left: 0px;
    top: 0px;
  }

  .fan-img{
    padding:1rem;
    border-radius: 50%;
  }
  .fan-img>img{
    border-radius: 50%;
    border: 5px solid rgba(97, 73, 62, 1);
    background-color: rgb(254, 252, 229);
    width: 100%;
    height: 100%;
    max-height: 450px;
    max-width: 450px;
  }

  .btn-group{
    text-align: center;
  }

  .btn {
    height: 70px;
    border-radius: 35px;
    margin-left: 5px;
    margin-right: 5px;
    width: 70px;
    box-shadow: 4px 4px 4px 0 rgb(0 0 0 / 30%);
    background-color: rgba(249, 234, 178, 1);
    border: 4px solid #61493E;
    cursor: pointer;
  }
  .btn>span{
    color: #61493E;
    font-size: 24px;
  }
  .top{
    color: #61493E;
  }

  .active {
    box-shadow: 0 8px 0 0 rgb(0 0 0 / 20%), 0 6px 0 0 rgb(0 0 0 / 19%);
    background-color: rgba(235, 174, 144, 1);
  }

  .nav-right{
    font-size: 12px;
    position: absolute;
    top: 0px;
    right: 0px;
    padding: 10px;
    border-radius: 18px;
    background-color: #FEEEAB;
    margin: 3% 3% 0 0;
    color: #61493E;
  }



  @keyframes rotation-one {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes rotation-one {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@-o-keyframes rotation-one {
    0% {
        -o-transform: rotate(0deg);
    }
    100% {
        -o-transform: rotate(360deg);
    }
}

@-moz-keyframes rotation-one {
    0% {
        -moz-transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(360deg);
    }
}

@keyframes rotation-two {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(720deg);
    }
}

@-webkit-keyframes rotation-two {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(720deg);
    }
}

@-moz-keyframes rotation-two {
    0% {
        -moz-transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(720deg);
    }
}

@-o-keyframes rotation-two {
    0% {
        -o-transform: rotate(0deg);
    }
    100% {
        -o-transform: rotate(720deg);
    }
}

@keyframes rotation-thr {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(1440deg);
    }
}

@-webkit-keyframes rotation-thr {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(1440deg);
    }
}

@-moz-keyframes rotation-thr {
    0% {
        -moz-transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(1440deg);
    }
}

@-o-keyframes rotation-thr {
    0% {
        -o-transform: rotate(0deg);
    }
    100% {
        -o-transform: rotate(1440deg);
    }
}


.run-1 {
    -webkit-animation: rotation-one 1s linear infinite;
    animation: rotation-one 1s linear infinite;
    -moz-animation: rotation-one 1s linear infinite;
    -o-animation: rotation-one 1s linear infinite;
}

.run-2 {
    -webkit-animation: rotation-two 1s linear infinite;
    animation: rotation-two 1s linear infinite;
    -moz-animation: rotation-two 1s linear infinite;
    -o-animation: rotation-two 1s linear infinite;
}

.run-3 {
    -webkit-animation: rotation-thr 1s linear infinite;
    animation: rotation-thr 1s linear infinite;
    -moz-animation: rotation-thr 1s linear infinite;
    -o-animation: rotation-thr 1s linear infinite;
}

.stop-1 {
    -moz-transform: rotate(-720deg);
    -o-transform: rotate(-720deg);
    transform: rotate(-720deg);
    -webkit-transform: rotate(-720deg);
    animation: a-one-stop 2s ease-out;
    -moz-animation: a-one-stop 2s ease-out;
    -webkit-animation: a-one-stop 2s ease-out;
    -o-animation: a-one-stop 2s ease-out;
}

.stop-2 {
    -o-transform: rotate(-1080deg);
    -moz-transform: rotate(-1080deg);
    transform: rotate(-1080deg);
    -webkit-transform: rotate(-1080deg);
    animation: a-one-stop 2s ease-out;
    -moz-animation: a-one-stop 2s ease-out;
    -webkit-animation: a-one-stop 2s ease-out;
    -o-animation: a-one-stop 2s ease-out;
}

.stop-3 {
    transform: rotate(-1440deg);
    -o-transform: rotate(-1440deg);
    -moz-transform: rotate(-1440deg);
    -webkit-transform: rotate(-1440deg);
    animation: a-one-stop 2s ease-out;
    -moz-animation: a-one-stop 2s ease-out;
    -webkit-animation: a-one-stop 2s ease-out;
    -o-animation: a-one-stop 2s ease-out;
}


.start-1 {
  -webkit-animation: a-one-start 2s ease-in;
  animation: a-one-start 2s ease-in;
  -moz-animation: a-one-start 2s ease-in;
  -o-animation: a-one-start 2s ease-in;
}

.start-2 {
  -webkit-animation: a-two-start 2s ease-in;
  animation: a-two-start 2s ease-in;
  -moz-animation: a-two-start 2s ease-in;
  -o-animation: a-two-start 2s ease-in;
}

.start-3 {
  -webkit-animation: a-thr-start 2s ease-in;
  animation: a-thr-start 2s ease-in;
  -moz-animation: a-thr-start 2s ease-in;
  -o-animation: a-thr-start 2s ease-in;
}

@keyframes a-one-start {
    100% {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes a-one-start {
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@-moz-keyframes a-one-start {
    100% {
        -moz-transform: rotate(360deg);
    }
}

@-o-keyframes a-one-start {
    100% {
        -o-transform: rotate(360deg);
    }
}

@keyframes a-two-start {
    100% {
        transform: rotate(720deg);
    }
}

@-webkit-keyframes a-two-start {
    100% {
        -webkit-transform: rotate(720deg);
    }
}

@-moz-keyframes a-two-start {
    100% {
        -moz-transform: rotate(720deg);
    }
}

@-o-keyframes a-two-start {
    100% {
        -o-transform: rotate(720deg);
    }
}

@keyframes a-thr-start {
    100% {
        transform: rotate(1800deg);
    }
}

@-webkit-keyframes a-thr-start {
    100% {
        -webkit-transform: rotate(1800deg);
    }
}

@-moz-keyframes a-thr-start {
    100% {
        -moz-transform: rotate(1800deg);
    }
}

@-o-keyframes a-thr-start {
    100% {
        -o-transform: rotate(1800deg);
    }
}

@keyframes a-one-stop {
    100% {
        transform: rotate(-360deg);
    }
}

@-webkit-keyframes a-one-stop {
    100% {
        -webkit-transform: rotate(-360deg);
    }
}

@-moz-keyframes a-one-stop {
    100% {
        -moz-transform: rotate(-360deg);
    }
}

@-o-keyframes a-one-stop {
    100% {
        -o-transform: rotate(-360deg);
    }
}

@keyframes a-two-stop {
    100% {
        transform: rotate(-720deg);
    }
}

@-webkit-keyframes a-two-stop {
    100% {
        -webkit-transform: rotate(-720deg);
    }
}

@-moz-keyframes a-two-stop {
    100% {
        -moz-transform: rotate(-720deg);
    }
}

@-o-keyframes a-two-stop {
    100% {
        -o-transform: rotate(-720deg);
    }
}

@keyframes a-thr-stop {
    100% {
        transform: rotate(-1440deg);
    }
}

@-webkit-keyframes a-thr-stop {
    100% {
        -webkit-transform: rotate(-1440deg);
    }
}

@-moz-keyframes a-thr-stop {
    100% {
        -moz-transform: rotate(-1440deg);
    }
}

@-o-keyframes a-thr-stop {
    100% {
        -o-transform: rotate(-1440deg);
    }
}
