<!DOCTYPE html>
<html lang="en">
<head>
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="assets/image/icon.png"/>
  <script type="text/javascript" src="assets/vue.min.js"></script>
  <title>茫茫然</title>
</head>
</head>
<body>
  <div id="app" class="container">
    <div class="header">
      <img src="assets/image/icon.png" alt="这是我" width="160" height="160">
    </div>
    <div class="content">
      <div><h1 class="name">茫然</h1></div>
      <div class="text"><p>没有理由不让自己变得更好</p></div>
      <div class="text"><p>不要熬夜多喝水，少吃零食多运动，身体健康最重要，人生没有来不及</p></div>
      <div class="deny-box">
        <div class="deny-div" style="margin-left: 2rem;">一四年接触编程，曾经喜欢</div>
        <div class="deny-div"><span class="deny-text">幻想</span><del>折腾</del>，</div>
        <div class="deny-div">现在</div>
        <div class="deny-div"><span class="deny-text">随便搞搞</span><del>追求从简</del>，</div>
        <div class="deny-div">热爱</div>
        <div class="deny-div"><span class="deny-text">走弯路</span><del>新特性</del>。</div>
      </div>
      <div class="text">
        <p>
          本人
          <span v-on:mouseover="changeActive($event,'四舍五入')" v-on:mouseout="removeActive($event,'身高两米')" class="change-text">身高两米</span>
          ，热衷于
          <span v-on:mouseover="changeActive($event,'间歇性学习')" v-on:mouseout="removeActive($event,'学习新知识')" class="change-text">学习新知识</span>
          ，喜欢收集
          <span v-on:mouseover="changeActive($event,'无趣')" v-on:mouseout="removeActive($event,'有趣')" class="change-text">有趣</span>
          的东西
        </p>
      </div>
      <div class="container-tag">
        <div class="container-img flip-wrapper" onclick="changeClass(this)">
          <div class="flip-a">
            <img src="assets/image/icon.png" alt="这是我" width="200" height="200" class="coverd">
          </div>
          <div class="flip-b">
            <div style="height: 200px;">
              <p class="flip-font">啥也没有</p>
              <p>👆点头像</p>
            </div>
          </div>
          <div class="content-tag">
            <span>点我</span>
          </div>
        </div>
      </div>
    </div>
    <div class="header">
      <span>联系我</span><span class="o-vertical">邮箱：<EMAIL></span>
      <span class="footer-a"><a href="#" class="footer-a">关于</a></span>
    </div>
  </div>
</body>
<script>
  new Vue({ 
    el: '#app',
    data: {
    },
    methods: {
      changeActive($event,text){
        $event.target.innerHTML = text
      },
      removeActive($event,text){
        $event.target.innerHTML = text
      },
      toUrl(url){
        window.location.href = url
      }

    },
  })

  function toUrl(url){
    window.location.href = url
  }

  var isUp = true
  function changeClass(el) {
    if (isUp) {
      el.className = 'flip-wrapper is-flipped';
    } else {
      el.className = 'flip-wrapper';
    }
    isUp = !isUp
  }
</script>

<style>
  @font-face {
    font-family: "苏新诗柳楷简";
    src: url(assets/font/sxslkj.ttf);
  }
  .container{
    padding-top: 6%;
    width: 90%;
    margin: auto;
    text-align: center;
  }

  .name{
    font-size: calc(1.375rem + 1.5vw);
    margin-top: 0;
    font-weight: 500;
    line-height: 2rem;
    margin-bottom: 2rem;
    text-align: center;
  }
  .content {
    text-align: center;
    font-family: "苏新诗柳楷简", "STKaiti", "KaiTi", cursive;
    color: #7B5252;
    font-size: 1.3rem;
    text-align: left;
  }
  .text{
    text-indent: 2rem;
    margin-bottom: 2rem;
  }
  .content>p>del{
    opacity: 0.3;
  }
  .content-a{
    text-decoration:none;
    cursor: pointer;
  }
  .change-text{
    cursor: pointer;
  }
  .change-text:hover{
    background-color: #d48806;
    color: white;
    border-radius: 3px;
  }

  .container-tag{
    width:200px;
    height:200px;
    position:relative;
    background: #fff;
    overflow: hidden;
    border-radius: 5px;
    margin: 0 auto;
    cursor: pointer;
  }
  .content-tag{
    width:100px;
    height:100px;
    position: absolute;
    top:-50px;
    right:-50px;
    transform: rotate(45deg);
  }
  .content-tag span{
    position: absolute;
    bottom:0;
    display: block;
    font-size:16px;
    color:#fff;
    background: #36A8FF;
    width:100px;
    text-align: center;
  }
  .container-img{
    width:200px;
    height:200px;
  }
  /* 3d翻转 */
  .flip-wrapper {
    position: relative;
    transition: all .25s ease-in-out;
    transform-style: preserve-3d;
  }
  .flip-wrapper.is-flipped {
    transform: rotateY(180deg);
  }
  .flip-b {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    transform: rotateY(-180deg);
    text-align: center;
    color: #262626;
    background-color: #fffbe6;
    backface-visibility: hidden;
  }
  .flip-a {
    height: 100%;
    backface-visibility: hidden;
  }
  .header{
    text-align: center;
    margin: 20px auto;
    color: #8c8c8c;
    border-radius: 50%;
    overflow: hidden;
  }
  .header>img{
    border-radius: 5px;
    cursor: pointer;
  }
  .o-vertical{
    padding: 0 10px;
    margin: 0 10px;
    border-left: solid 1px #8c8c8c;
    border-right: solid 1px #8c8c8c;
  }
  .flip-font{
    font-size: 2.5rem;
    font-weight: 500;
    margin-top: 60px;
  }

  .deny-div{
    float: left;
    font-size: 20px;
    position:relative;
  }
  .deny-box{
    overflow: hidden;
    padding-bottom: 1.5rem;
    /*禁止选择*/
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }
  .deny-text{
    position:absolute;
    top: 12px;
    left: 15px;
    font-size: 18px;
    color: #d9d9d9;
    z-index: -1;
  }

  .footer-a{
    text-decoration: none;
    color: #909399;
  }
  .footer-a:hover{
    color: #606266;
  }
</style>
</html>