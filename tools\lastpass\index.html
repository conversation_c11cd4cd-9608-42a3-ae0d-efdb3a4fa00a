<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <link rel="shortcut icon" href="../../door/assets/icon/logo.png" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/css/index.css">
  <link rel="stylesheet" href="assets/css/header.css">
  <link rel="stylesheet" href="assets/css/card.css">
  <link rel="stylesheet" href="assets/css/modal.css">
  <link rel="stylesheet" href="assets/css/form.css">
  <title>超级密码本</title>
</head>
<body>
<div id="app">
<div class="header-container">
  <div class="header">
    <h1 class="header-title">超级密码本</h1>
    <div class="search-container">
      <div class="search-section">
        <select id="searchFilter">
          <option value="">全部</option>
          <option value="system">系统</option>
          <option value="database">数据库</option>
          <option value="n4a">4A</option>
          <option value="link">链接</option>
        </select>
        <input type="text" id="searchQuery" class="search-input" placeholder="关键字搜索...">
      </div>
      <button class="action-btn" id="addBtn">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" height="24" width="24">
        <path fill="white" d="M23.15 2.587L18.21 0.210001C17.9308 0.075557 17.6167 0.031246 17.3113 0.083204C17.0058 0.135162 16.724 0.280818 16.505 0.500001L7.04499 9.13L2.92499 6.002C2.73912 5.86101 2.50976 5.78953 2.27669 5.79994C2.04363 5.81035 1.82156 5.902 1.64899 6.059L0.326993 7.261C0.223973 7.35465 0.141644 7.46878 0.0852761 7.59608C0.0289081 7.72339 -0.00025659 7.86106 -0.000350724 8.00028C-0.000444857 8.1395 0.0285336 8.27721 0.0847294 8.40459C0.140925 8.53197 0.2231 8.64621 0.325993 8.74L3.89899 12L0.325993 15.26C0.2231 15.3538 0.140925 15.468 0.0847294 15.5954C0.0285336 15.7228 -0.000444857 15.8605 -0.000350724 15.9997C-0.00025659 16.1389 0.0289081 16.2766 0.0852761 16.4039C0.141644 16.5312 0.223973 16.6454 0.326993 16.739L1.64999 17.94C1.82256 18.097 2.04463 18.1887 2.27769 18.1991C2.51076 18.2095 2.74012 18.138 2.92599 17.997L7.04599 14.869L16.506 23.499C16.7248 23.7182 17.0064 23.8639 17.3117 23.9159C17.6171 23.9679 17.931 23.9235 18.21 23.789L23.152 21.412C23.4062 21.2893 23.6207 21.0973 23.7707 20.8581C23.9207 20.619 24.0002 20.3423 24 20.06V3.939C24 3.65647 23.9203 3.37967 23.7699 3.14048C23.6195 2.90129 23.4046 2.70943 23.15 2.587ZM18.004 17.448L10.826 12L18.004 6.552V17.448Z"></path>
        </svg>
        <p class="text">添加新密码</p>
      </button>
      <div class="import-export">
          <button class="action-btn" id="exportBtn">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" height="24" width="24">
          <path fill="white" d="M23.15 2.587L18.21 0.210001C17.9308 0.075557 17.6167 0.031246 17.3113 0.083204C17.0058 0.135162 16.724 0.280818 16.505 0.500001L7.04499 9.13L2.92499 6.002C2.73912 5.86101 2.50976 5.78953 2.27669 5.79994C2.04363 5.81035 1.82156 5.902 1.64899 6.059L0.326993 7.261C0.223973 7.35465 0.141644 7.46878 0.0852761 7.59608C0.0289081 7.72339 -0.00025659 7.86106 -0.000350724 8.00028C-0.000444857 8.1395 0.0285336 8.27721 0.0847294 8.40459C0.140925 8.53197 0.2231 8.64621 0.325993 8.74L3.89899 12L0.325993 15.26C0.2231 15.3538 0.140925 15.468 0.0847294 15.5954C0.0285336 15.7228 -0.000444857 15.8605 -0.000350724 15.9997C-0.00025659 16.1389 0.0289081 16.2766 0.0852761 16.4039C0.141644 16.5312 0.223973 16.6454 0.326993 16.739L1.64999 17.94C1.82256 18.097 2.04463 18.1887 2.27769 18.1991C2.51076 18.2095 2.74012 18.138 2.92599 17.997L7.04599 14.869L16.506 23.499C16.7248 23.7182 17.0064 23.8639 17.3117 23.9159C17.6171 23.9679 17.931 23.9235 18.21 23.789L23.152 21.412C23.4062 21.2893 23.6207 21.0973 23.7707 20.8581C23.9207 20.619 24.0002 20.3423 24 20.06V3.939C24 3.65647 23.9203 3.37967 23.7699 3.14048C23.6195 2.90129 23.4046 2.70943 23.15 2.587ZM18.004 17.448L10.826 12L18.004 6.552V17.448Z"></path>
          </svg>
          <p class="text">导出配置</p>
        </button>
        <input type="file" id="importFile" style="display: none;">
        <button class="action-btn" id="importBtn">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" height="24" width="24">
          <path fill="white" d="M23.15 2.587L18.21 0.210001C17.9308 0.075557 17.6167 0.031246 17.3113 0.083204C17.0058 0.135162 16.724 0.280818 16.505 0.500001L7.04499 9.13L2.92499 6.002C2.73912 5.86101 2.50976 5.78953 2.27669 5.79994C2.04363 5.81035 1.82156 5.902 1.64899 6.059L0.326993 7.261C0.223973 7.35465 0.141644 7.46878 0.0852761 7.59608C0.0289081 7.72339 -0.00025659 7.86106 -0.000350724 8.00028C-0.000444857 8.1395 0.0285336 8.27721 0.0847294 8.40459C0.140925 8.53197 0.2231 8.64621 0.325993 8.74L3.89899 12L0.325993 15.26C0.2231 15.3538 0.140925 15.468 0.0847294 15.5954C0.0285336 15.7228 -0.000444857 15.8605 -0.000350724 15.9997C-0.00025659 16.1389 0.0289081 16.2766 0.0852761 16.4039C0.141644 16.5312 0.223973 16.6454 0.326993 16.739L1.64999 17.94C1.82256 18.097 2.04463 18.1887 2.27769 18.1991C2.51076 18.2095 2.74012 18.138 2.92599 17.997L7.04599 14.869L16.506 23.499C16.7248 23.7182 17.0064 23.8639 17.3117 23.9159C17.6171 23.9679 17.931 23.9235 18.21 23.789L23.152 21.412C23.4062 21.2893 23.6207 21.0973 23.7707 20.8581C23.9207 20.619 24.0002 20.3423 24 20.06V3.939C24 3.65647 23.9203 3.37967 23.7699 3.14048C23.6195 2.90129 23.4046 2.70943 23.15 2.587ZM18.004 17.448L10.826 12L18.004 6.552V17.448Z"></path>
          </svg>
          <p class="text">导入配置</p>
        </button>
      </div>
    </div>
  </div>
</div>
<div class="main-container">
  <!-- 主要内容区域 -->
  <div class="main-content">
    <!-- 密码卡片列表 -->
    <div id="cardWarper" class="card-warper"></div>
    <!-- 空状态提示 -->
    <div class="empty-state" id="emptyState" style="display:none;">
      <div class="empty-state-content">
        <h3>还没有添加任何密码</h3>
        <p>点击"添加新密码"按钮开始使用密码本</p>
      </div>
    </div>
  </div>
</div>

<!-- 添加/编辑表单对话框 -->
<div class="modal" id="modal" style="display:none;">
  <div class="modal-content">
    <button class="modal-close" id="closeModalBtn"></button>
    <div class="modal-form-container">
      <h2 id="modalTitle">编辑密码本</h2>
      <div id="index" hidden>ID</div>
      <div class="form-container">
        <div class="form-warper">
          <div class="form-group">
            <select id="type">
              <option value="system">系统</option>
              <option value="database">数据库</option>
              <option value="n4a">4A</option>
              <option value="link">纯链接</option>
            </select>
            <select id="status">
              <option value="1">正常</option>
              <option value="2">过期</option>
              <option value="3">错误</option>
              <option value="4">删除</option>
            </select>
          </div>
          <div id="formContent">
            <div class="form-group">
              <input type="text" id="address" placeholder="‎" required>
              <label for="address">地址</label>
            </div>
            <div class="form-group">
              <input type="text" id="username" placeholder="‎" required>
              <label for="username">账号</label>
            </div>
            <div class="form-group">    
              <input type="password" id="password" placeholder="‎" required>
              <label for="password" >密码</label>
            </div>
            <div class="form-group">
              <input type="text" id="secret" placeholder="‎">
              <label>4A密钥</label>
            </div>
          </div>
          <div class="form-group">
            <textarea id="remark" placeholder="‎" rows="3"></textarea>
            <label>备注</label>
          </div>
          
          <div class="form-actions">
            <button type="button" class="primary" id="saveBtn">保存</button>
            <button type="button" id="cancelBtn">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
  <!-- 提示框容器 -->
  <div class="toast-container" id="toastContainer"></div>
</div>
<script src="assets/form.js"></script>
<script src="assets/card.js"></script>
<script src="assets/index.js"></script>
<script>
// 密码显示/隐藏
document.getElementById('togglePwd').onclick = function() {
  const pwd = document.getElementById('password');
  if (pwd.type === 'password') {
    pwd.type = 'text';
    this.textContent = '🙈';
  } else {
    pwd.type = 'password';
    this.textContent = '👁️';
  }
};
// ESC关闭弹窗
window.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    document.getElementById('modal').style.display = 'none';
  }
});
// 表单弹窗打开时自动聚焦
window.openPasswordForm = (function(orig){
  return function(data, cb) {
    orig(data, cb);
    setTimeout(() => {
      let first = document.querySelector('#modal input:not([type=hidden]), #modal textarea');
      if(first) first.focus();
    }, 100);
  }
})(window.openPasswordForm);
</script>
</body>
</html>