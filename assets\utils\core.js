
/* 基本封装 */
function $(id) {
    return document.getElementById(id);
}

function className(name){
    return document.getElementsByClassName(name)[0]
}

function removeClass(ele,txt){
	let str =  ele.className,index = str.indexOf(txt);
	if(index > -1){
		ele.className = str.replace(txt,"").trim();
	}
}
function addClass(ele,txt){
    ele.className += ' ' +txt;
}


function removeClassItem(className,txt){
    let ele = document.getElementsByClassName(className);
    for (let i = 0; i < ele.length; i++) {
      ele[i].classList.remove(txt);
    }
}



/**
 * 检查给定值是否为空。
 * 支持：null, undefined, 空字符串, 空数组, 空对象
 * @param {*} value - 要检查的值
 * @returns {boolean} - 如果值被认为是“空”，则返回true
 */
function isEmpty(value) {
  if (value == null) {
    // 处理 null 和 undefined
    return true;
  }

  if (value == 'undefined') {
    // 处理 null 和 undefined
    return true;
  }

  if (typeof value === 'string' || Array.isArray(value)) {
    // 字符串或数组，考虑trim()以忽略空白字符对字符串的影响
    return value.length === 0 || (typeof value === 'string' && value.trim().length === 0);
  }

  if (typeof value === 'object') {
    // 对象，使用Object.keys判断是否有自己的属性
    return Object.keys(value).length === 0;
  }

  if (typeof value === 'number' && isNaN(value)) {
    // NaN被视为特殊类型的空值
    return true;
  }

  // 默认返回false，即非空
  return false;
}
