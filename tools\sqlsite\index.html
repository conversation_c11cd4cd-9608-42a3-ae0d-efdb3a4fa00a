<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8" />
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" /><!-- 兼容标签 -->
  <link rel="shortcut icon" href="../../door/assets/icon/logo.png" type="image/x-icon" />
  <link rel="stylesheet" href="assets/css/index.css">
  <link rel="stylesheet" href="assets/css/table.css">
  <title>SQL生成表</title>
</head>
<body>
<div id="app">
<div class="container">
<div class="sql-container">
  <div class="sql-warper">
    <h1>SQL生成表</h1>
    <div class="sql-input" id="sqlInput">
      <textarea id="sqlInput" placeholder="粘贴或输入SQL建表语句，支持MySQL/SQLServer/高斯/Oracle等"></textarea>
    </div>
    <div class="sql-tabs" id="sqlTabs">
      <button onclick="showSQL('mysql')" id="tab-mysql" class="active">MySQL</button>
      <button onclick="showSQL('oracle')" id="tab-oracle">Oracle</button>
      <button onclick="showSQL('sqlserver')" id="tab-sqlserver">SQLServer</button>
      <button onclick="showSQL('gauss')" id="tab-gauss">高斯</button>
    </div>
    <h1>表格生成SQL</h1>
    <div class="sql-table" id="sqlTable">
      <h3>表名：<input id="editable-table-name" value=""/></h3>
      <table class="result-table" id="editable-table">
        <tr><th>字段名</th><th>类型</th><th>约束</th><th>操作</th></tr>
    </div>
    <div class="sql-output" id="sqlOutput" style="display:none;"></div>
  </div>
</div>
</div>
</div>
<script src="assets/index.js"></script>
<script src="assets/table.js"></script>
<script src="assets/output.js"></script>
</body>
</html>
