<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <!-- <link rel="shortcut icon" href="assets/image/9.jpeg" type="image/x-icon"/> -->
  <title>跳跳</title>
</head>
<body>
<div id="app">
  <div class="wrap" id="wrap"></div>
  <div class="footer" id="footer"> - THE END - </div>
</div>
</body>

<script>
  var data = [
    {id:1, name:'',url:'cat/1.jpg',alt:''},
    {id:2, name:'',url:'cat/2.jpg',alt:''},
    {id:3, name:'',url:'cat/3.jpg',alt:''},
    {id:5, name:'',url:'cat/5.jpg',alt:''},
    {id:6, name:'',url:'cat/6.jpg',alt:''},
    {id:7, name:'',url:'cat/7.jpg',alt:''},
    {id:8, name:'',url:'cat/8.jpg',alt:''},
    {id:9, name:'',url:'cat/9.jpg',alt:''},
    {id:10,name:'',url:'cat/10.jpg',alt:''},
    {id:11,name:'',url:'cat/11.jpg',alt:''},
    {id:12,name:'',url:'cat/12.jpg',alt:''},
    {id:13,name:'',url:'cat/13.jpg',alt:''},
    {id:14,name:'',url:'cat/14.jpg',alt:''},
    {id:15,name:'',url:'cat/15.jpg',alt:''},
    {id:16,name:'',url:'cat/16.png',alt:''},
    {id:17,name:'',url:'cat/17.jpg',alt:''},
    {id:18,name:'',url:'cat/18.jpg',alt:''},
    {id:19,name:'',url:'cat/19.jpg',alt:''},
    {id:20,name:'',url:'cat/20.jpg',alt:''},
    {id:21,name:'',url:'cat/21.jpg',alt:''},
    {id:22,name:'',url:'cat/22.jpg',alt:''},
    {id:23,name:'',url:'cat/23.jpg',alt:''},
    {id:24,name:'',url:'cat/24.jpg',alt:''},
    {id:25,name:'',url:'cat/25.jpg',alt:''},
    {id:26,name:'',url:'cat/26.jpg',alt:''},
    {id:27,name:'',url:'cat/27.jpg',alt:''},
    {id:28,name:'',url:'cat/28.jpg',alt:''},
    {id:29,name:'',url:'cat/29.jpg',alt:''},
    {id:30,name:'',url:'cat/30.jpg',alt:''},
    {id:31,name:'',url:'cat/31.jpg',alt:''},
    {id:32,name:'',url:'cat/32.jpg',alt:''},
    {id:33,name:'',url:'cat/33.jpg',alt:''},
    {id:34,name:'',url:'cat/34.jpg',alt:''},
  ]

  function $(id){
    return document.getElementById(id);
  }

  // 根据元素宽度生成列数从而实现响应式
  function createColumns(ele) {
    let columns = 0
    let width = ele.offsetWidth;
    if(width < 360){ columns = 1 }
    else if (width < 768) { columns = 2; }
    else if (width < 992) { columns = 3; }
    else if (width < 1200) { columns = 4; }
    else if (width < 1400) { columns = 5; }
    else { columns = 6; }
    return columns;
  }

  var timer = null; //一个计时器的id
  var index = 0;
  var wrapDome = document.querySelector(".wrap"); //父容器
  var columns = createColumns(wrapDome); //列数
  var heightArr = new Array() //每一列高度数组
  var spacing = 10; //间距
  var colWidth = Math.floor((wrapDome.offsetWidth - ((columns - 1) * spacing)) / columns); //列宽
  var errorImg = 0

  // console.log('index='+index+',columns='+columns+',data.length='+data.length+',wrapDome.offsetWidth='+wrapDome.offsetWidth)
  function create(){
    // console.log('create()')
    let observer = new IntersectionObserver(callback)
    function callback(entries){
      if(entries[0].isIntersecting){  // 如果已进入视图，停止监听，并且生成新的元素
        observer.unobserve(divDom)
        create()
      }
    }
    let divDom = document.createElement('div')
    var img = new Image();
    img.src = '../../../assets/images/'+ data[index].url
    // console.log('index1='+index+',columns='+columns)
    // 给图片对象添加onload事件监听器
    let i = index
    // console.log('index='+index+',columns='+columns+',data.length='+data.length+',wrapDome.offsetWidth='+wrapDome.offsetWidth)
    img.onload = function() {
      console.log('index2='+i+',columns='+columns)
      // 图片加载完成后的操作
      divDom.setAttribute('class','item')
      divDom.appendChild(img)
      $('wrap').appendChild(divDom)
      let rota = colWidth/img.width
      let height = img.height*rota
      // console.log('img.height='+img.height+',img.width='+img.width+',colWidth='+colWidth+',rota='+rota+',height='+height)
      divDom.style.width = colWidth + 'px'
      if (i < columns) {
        heightArr[i] = height + 14; //将每一列的高度存放到_arr数组中
        // console.log(heightArr)
        divDom.style.position='absolute'//style设置样式
        divDom.style.top = 14 + 'px';
        divDom.style.left = (colWidth + spacing) * i + "px";
      } else {
        let min = Math.min(...heightArr); //最小高度
        let seq = heightArr.indexOf(min); //最小高度的索引
        divDom.style.position='absolute'//style设置样式
        divDom.style.top = min + spacing + "px"; //第二列居上距离
        divDom.style.left = (spacing + colWidth) * seq + "px"; //第二列居左距离
        heightArr[seq] += height + spacing;
      }
      if (i+1==data.length){
        $('wrap').style.height = Math.max(...heightArr) + 'px'
        $('footer').style.display = 'block'
      }
      // console.log('img.height='+img.height+',img.width='+img.width+',colWidth='+colWidth+',rota='+rota+',height='+height)
    }
    img.onerror = function(){
      console.log('图片加载失败='+i)
      errorImg = errorImg + 1
      create()
    }
    index = index + 1
    if(index < data.length){
      observer.observe(divDom)   // 观察该元素用作懒加载
    }else{
      console.log(" - THE END - ")
    }
  }
  create()

  window.onresize = function () {
    //窗口尺寸变动后，重新排列
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(render, 100);
  }

  window.addEventListener("load", render);

  // 根据元素宽度生成列数从而实现响应式
  function render() {
    heightArr = [] //每一列高度数组
    columns = createColumns(wrapDome); //列数
    colWidth = (wrapDome.offsetWidth - (columns - 1) * spacing) / columns; //列宽
    let items = document.querySelectorAll(".item");
    console.log("重新排列：" + items.length+',data.length='+data.length)
    for (let i = 0; i < items.length; i++) {
      items[i].style.width = colWidth + "px";
      if (i < columns) {
        // console.log("第几条"+i)
        heightArr.push(items[i].offsetHeight + 14); //将每一列的高度存放到_arr数组中
        items[i].style.position='absolute'//style设置样式
        items[i].style.top = 14 + 'px';
        items[i].style.left = (colWidth + spacing) * i + "px";
      } else {
        let min = Math.min(...heightArr); //最小高度
        let seq = heightArr.indexOf(min); //最小高度的索引
        items[i].style.position='absolute'//style设置样式
        items[i].style.top = min + spacing + "px"; //第二列居上距离
        items[i].style.left = (spacing + colWidth) * seq + "px"; //第二列居左距离
        heightArr[seq] += items[i].offsetHeight + spacing;
      }
    }
    if ((items.length+errorImg)==data.length){
      $('wrap').style.height = Math.max(...heightArr) + 'px'
      $('footer').style.display = 'block'
    }
  }
  // function render() {
  //   let heightArr = [] //每一列高度数组
  //   let wrapDome = document.querySelector(".wrap"); //父容器
  //   let columns = createColumns(wrapDome); //列数
  //   let spacing = 10; //间距
  //   let colWidth = (wrapDome.offsetWidth - (columns - 1) * spacing) / columns; //列宽
  //   let items = document.querySelectorAll(".item");
  //   console.log("重新排列：" + items.length)
  //   for (let i = 0; i < items.length; i++) {
  //     items[i].style.width = colWidth + "px";
  //     if (i < columns) {
  //       // console.log("第几条"+i)
  //       heightArr.push(items[i].offsetHeight); //将每一列的高度存放到_arr数组中
  //       items[i].style.position='absolute'//style设置样式
  //       items[i].style.top = 14 + 'px';
  //       items[i].style.left = (colWidth + spacing) * i + "px";
  //     } else {
  //       let min = Math.min(...heightArr); //最小高度
  //       let index = heightArr.indexOf(min); //最小高度的索引
  //       items[i].style.position='absolute'//style设置样式
  //       items[i].style.top = min + spacing + "px"; //第二列居上距离
  //       items[i].style.left = (spacing + colWidth) * index + "px"; //第二列居左距离
  //       heightArr[index] += items[i].offsetHeight + spacing;
  //     }
  //   }
  //   // console.log(Math.max(...heightArr))
  //   $('wrap').style.height = (Math.max(...heightArr)+14) + 'px'
  // }
  
  // // 在网页上的资源全部加载完成后再运行代码
  // window.addEventListener("load", render);
  // // // 窗口宽度改变
  // window.addEventListener("resize", render);

</script>
<style>
  /* 禁止选择文字 */
  div{
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }

  body {
    margin: 0;
  }

  #app{
    width: 98%;
    margin: auto;
    height: 101vh;
  }

  .wrap {
    position: relative;
    width: 98%;
    margin: 0 auto;
  }

  .item {
    /* 图像灰度 */
    /* position: absolute; */
    float: left;
    filter: grayscale(0.5);
    transition: left 0.5s, top 0.5s;
  }

  .item img {
    display: block;
    width: 100%;
  }

  .footer{
    font-size: 0.7rem;
    text-align: center;
    line-height: 3rem;
    display: none;
    color: #969799;
  }
</style>
</html>