.main-container {
  margin: 2rem auto;
}

.main-content {
  width: 98%;
  max-width: 1200px;
  margin: 0 auto;
}


.card-item{
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 1rem;
  border-radius: 6px;
}

/* 密码卡片列表区域采用响应式Grid布局，最小宽度10rem */
.card-warper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15rem, 1fr));
  gap: 1.5rem;
  justify-content: center;
  width: 100%;
}
.card-header{
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
  display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 600;
}

.status-normal {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
}

.card-field {
  position: relative;
  margin-top: 2rem;
}


.card-field:hover > button {
  opacity: 1;
}

.card-field > input{
  display: block;
  width: 100%;
  padding: 0px;
  font-size: 16px;
  border: none;
  border-bottom: 2px solid #ccc;
  outline: none;
  background-color: transparent;
  text-indent: 6px;
}

/* Input label */
.card-field > label {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 12px;
  top: -24px;
  pointer-events: none;
  transition: all 0.3s ease;
  color: #007bff;
}

.card-field > span {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  width: 0;
  background-color: #007bff;
  transition: all 0.3s ease;
}

.card-field > button {
  cursor: pointer;
  position: absolute;
  bottom: 3px;
  height: 28px;
  right: 0;
  opacity: 0;
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: var(--border-radius);
  background-color: var(--primary-color);
  color: white;
  transition: var(--transition);
  font-weight: 500;
}

.card-field > input:focus + .card-field > label {
  top: -20px;
  font-size: 12px;
  color: #007bff;
}

.card-field > input:focus + .card-field > label + .card-field > span {
  width: 100%;
}


/* TOTP动态口令样式 */
.totp-container {
    width: 100%;
    margin: 4px 0;
    height: fit-content;
}

.totp-code {
    font-family: 'Roboto Mono', monospace;
    font-size: 26px;
    font-weight: 600;
    color: var(--primary-color);
    letter-spacing: 4px;
    text-align: center;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.totp-timer {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-bottom: 4px;
    font-weight: 500;
}

.totp-progress {
    width: 100%;
    height: 4px;
    background-color: rgba(0,0,0,0.1);
    border-radius: 2px;
    overflow: hidden;
}

.totp-progress-bar {
    height: 100%;
    background: linear-gradient(to right, var(--primary-color), #357abd);
    transition: width 1s linear;
}

.card-actions {
    margin-top: auto;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding-top: 8px;
    border-top: 1px solid #eee;
}

.card-actions button {
    padding: 4px 12px;
    font-size: 13px;
    min-width: 60px;
    border: none;
    border-radius: var(--border-radius);
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}
button.danger {
    background-color: var(--danger-color);
}


.status-normal { 
    background-color: rgba(46, 204, 113, 0.1); 
    color: var(--success-color);
}

.status-invalid { 
    background-color: rgba(241, 196, 15, 0.1); 
    color: var(--warning-color);
}

.status-deleted { 
    background-color: rgba(231, 76, 60, 0.1); 
    color: var(--danger-color);
}

.status-wrong {
    background-color: rgba(231, 76, 60, 0.1); 
    color: var(--danger-color);
}

.field-value{
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  line-height: 1.4;
  height: auto;
  margin-bottom: 10px;
}

