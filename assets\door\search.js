//选择显示隐藏
var searchIcon = $("search-icon"),searchEngine = $('search-engine');
document.onclick = function () {
    searchEngine.style.display = "none";
}

searchIcon.addEventListener('click', function (e) {
    stopFunc(e);
    searchEngine.style.display = "block";
}, false)

searchEngine.addEventListener('click', function (e) {
    stopFunc(e);
}, false)

//阻止事件向上传递
function stopFunc(e) { 
    e.stopPropagation ? e.stopPropagation() : e.cancelBubble = true;
}

var searchElement = $("search-input");
function search() {
    if (searchElement.value != "") { 
        window.open(searchElement.getAttribute("data-url") + encodeURIComponent(searchElement.value))
        searchElement.value = ""; 
    } 
    return false; 
}

function selectEngine(url,img){
    searchElement.setAttribute('data-url',url);
    $('search-img').src = img;
    searchEngine.style.display = "none";
}

const searchEngineItemElement = className('search-engine-item');
const searchEngineItemDemo = searchEngineItemElement.innerHTML;
searchEngineItemElement.innerHTML = '';
function renderSearch(){
    let searchHtml = '';
    if (searchData && searchData.length > 0) {
        searchData.forEach(searchItem => {
            searchHtml += searchEngineItemDemo.replace('{{url}}', searchItem.url)
                    .replace('{{name}}', searchItem.name)
                    .replace('{{img}}', searchItem.img)
                    .replace('{{_img}}', `<img src="${searchItem.img}">`);    
        })
        searchEngineItemElement.innerHTML = searchHtml;
    }
}

renderSearch();

