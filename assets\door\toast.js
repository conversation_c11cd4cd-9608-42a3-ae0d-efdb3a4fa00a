
    // Toast提示函数
  function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');

    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    // 根据类型设置图标
    let icon = '';
    switch(type) {
      case 'success':
        icon = '✓';
        break;
      case 'error':
        icon = '✗';
        break;
      case 'warning':
        icon = '⚠';
        break;
      default:
        icon = 'ℹ';
    }

    // 设置内容
    toast.innerHTML = `
      <div class="toast-icon">${icon}</div>
      <div class="toast-message">${message}</div>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
      toast.remove();
    }, 3000);
  }