<!DOCTYPE html>
<html lang="en">
<head>
  <!-- http-equiv:http头部协议，content：重要，显示网页模式，charset：特别重要，显示网页编码格式 -->
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <!-- width:可视区域宽度，initial-scale:首次打开无任何放大缩小，user-scalable：禁止缩放，貌似没生效 -->
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><!-- 兼容标签 -->
  <link rel="shortcut icon" href="../../../assets/icon/avatar.icon" type="image/x-icon"/>
  <title>计时器</title>
</head>
<body>
<div id="app">
  <div class="timer"><input type="text" id="timetext" value="00时00分00秒000毫秒" readonly></div>
  <div class="operate">
    <button type="button" onclick="start()">开始</button>
    <button type="button" onclick="stop()">暂停</button> 
    <button type="button" onclick="timing()">记时</button> 
    <button type="button" onclick="reset()">复位</button>
  </div>
  <div id="timing"></div>
</div>
</body>
<script>
  // 计时板
  var timingElement = document.getElementById('timing')
  //初始化变量
  var hour,minute,second;//时 分 秒
  hour=minute=second=0;//初始化
  var millisecond=0;//毫秒
  var int;
  var state = 0;//状态 0：初始化  1 启动 2 暂停  
  //重置函数
  function reset() {
    state = 0
    window.clearInterval(int);
    millisecond=hour=minute=second=0;
    document.getElementById('timetext').value='00时00分00秒000毫秒';
    
    var childs = timingElement.childNodes; 
    for(var i = childs .length - 1; i >= 0; i--) {
      timingElement.removeChild(childs[i]);
    }
  }
  //开始函数
  function start() {
    if(state==0||state==2){
      state = 1
    } else {
      return
    }
    int=setInterval(timer,11);//每隔50毫秒执行一次timer函数
  }
  //计时函数
  function timer() {
    millisecond=millisecond+11;
    if(millisecond>=1000) {
      millisecond=0;
      second=second+1;
    } if(second>=60) {
      second=0;
      minute=minute+1;
    } if(minute>=60) {
      minute=0;
      hour=hour+1;
    }
    document.getElementById('timetext').value=fillZero(hour)+'时'+fillZero(minute)+'分'+fillZero(second)+'秒'+fillZero2(millisecond)+'毫秒';
  }
  //暂停函数
  function stop() {
    state = 2
    window.clearInterval(int);
  }
  function timing(){
    let value = document.getElementById('timetext').value
    var div = document.createElement("div");
    div.innerText = value
    timingElement.insertBefore(div,timingElement.firstChild)
  }

  /**
   * 时间补位0
   */
  function fillZero(val){
    return val<10?'0' + val:val
  }

  function fillZero2(val){
    return val<10?'00' + val:val<100?'0'+val:val
  }

</script>
<style>
  /* 禁止选择文字 */
  div{
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
  }
  .timer{
    text-align: center;
    margin-top: 12%;
  }
  .timer > #timetext{
    width: 80%;
    min-width: 300px;
    border: none;
    font-size: 1.8rem;
    font-weight: bolder;
  }
  /** 输入框文字居中显示 */
  input[type='text']{
    display: block;
    margin: 0 auto;
    text-align: center;
  }

  .operate{
    text-align: center;
    margin: 1rem auto;
  }
  .operate > button{
    font-size: 1rem;
    padding: 1px 6px;
    margin: 0 6px;
  }
  #timing {
    text-align: center;
    color: slategray;
  }
</style>
</html>