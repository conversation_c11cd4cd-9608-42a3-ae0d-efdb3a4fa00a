
/** 单属性克制表 */
var restrain = [
  { atta:"草",attd:"水",resta:2 },
  { atta:"草",attd:"地面",resta:2 },
  { atta:"草",attd:"光",resta:2 },

  { atta:"草",attd:"草",resta:0.5 },
  { atta:"草",attd:"火",resta:0.5 },
  { atta:"草",attd:"飞行",resta:0.5 },
  { atta:"草",attd:"机械",resta:0.5 },
  { atta:"草",attd:"圣灵",resta:0.5 },
  { atta:"草",attd:"远古",resta:0.5 },
  { atta:"草",attd:"混沌",resta:0.5 },
  { atta:"草",attd:"神灵",resta:0.5 },

  { atta:"火",attd:"草",resta:2 },
  { atta:"火",attd:"机械",resta:2 },
  { atta:"火",attd:"冰",resta:2 },

  { atta:"火",attd:"水",resta:0.5 },
  { atta:"火",attd:"火",resta:0.5 },
  { atta:"火",attd:"圣灵",resta:0.5 },
  { atta:"火",attd:"自然",resta:0.5 },
  { atta:"火",attd:"混沌",resta:0.5 },
  { atta:"火",attd:"神灵",resta:0.5 },

  { atta:"水",attd:"火",resta:2 },
  { atta:"水",attd:"地面",resta:2 },

  { atta:"水",attd:"草",resta:0.5 },
  { atta:"水",attd:"水",resta:0.5 },
  { atta:"水",attd:"圣灵",resta:0.5 },
  { atta:"水",attd:"自然",resta:0.5 },
  { atta:"水",attd:"混沌",resta:0.5 },
  { atta:"水",attd:"神灵",resta:0.5 },

  { atta:"暗影",attd:"超能",resta:2 },
  { atta:"暗影",attd:"暗影",resta:2 },
  { atta:"暗影",attd:"次元",resta:2 },

  { atta:"暗影",attd:"机械",resta:0.5 },
  { atta:"暗影",attd:"冰",resta:0.5 },
  { atta:"暗影",attd:"光",resta:0.5 },
  { atta:"暗影",attd:"圣灵",resta:0.5 },
  { atta:"暗影",attd:"邪灵",resta:0.5 },
  { atta:"暗影",attd:"神灵",resta:0.5 },

  { atta:"冰",attd:"草",resta:2 },
  { atta:"冰",attd:"飞行",resta:2 },
  { atta:"冰",attd:"地面",resta:2 },
  { atta:"冰",attd:"次元",resta:2 },
  { atta:"冰",attd:"远古",resta:2 },
  { atta:"冰",attd:"轮回",resta:2 },
  { atta:"冰",attd:"虫",resta:2 },
  
  { atta:"冰",attd:"水",resta:0.5 },
  { atta:"冰",attd:"火",resta:0.5 },
  { atta:"冰",attd:"机械",resta:0.5 },
  { atta:"冰",attd:"冰",resta:0.5 },
  { atta:"冰",attd:"圣灵",resta:0.5 },
  { atta:"冰",attd:"混沌",resta:0.5 },
  { atta:"冰",attd:"神灵",resta:0.5 },

  { atta:"超能",attd:"战斗",resta:2 },
  { atta:"超能",attd:"神秘",resta:2 },
  { atta:"超能",attd:"自然",resta:2 },

  { atta:"超能",attd:"机械",resta:0.5 },
  { atta:"超能",attd:"超能",resta:0.5 },
  { atta:"超能",attd:"虫",resta:0.5 },

  { atta:"超能",attd:"光",resta:0 },

  { atta:"虫",attd:"草",resta:2 },
  { atta:"虫",attd:"地面",resta:2 },
  { atta:"虫",attd:"战斗",resta:2 },
  { atta:"虫",attd:"混沌",resta:2 },
  { atta:"虫",attd:"虫",resta:2 },
  
  { atta:"虫",attd:"水",resta:0.5 },
  { atta:"虫",attd:"火",resta:0.5 },
  { atta:"虫",attd:"冰",resta:0.5 },
  { atta:"虫",attd:"光",resta:0.5 },
  
  { atta:"次元",attd:"飞行",resta:2 },
  { atta:"次元",attd:"机械",resta:2 },
  { atta:"次元",attd:"超能",resta:2 },
  { atta:"次元",attd:"邪灵",resta:2 },
  { atta:"次元",attd:"自然",resta:2 },
  { atta:"次元",attd:"虫",resta:2 },
  { atta:"次元",attd:"虚空",resta:2 },

  { atta:"次元",attd:"冰",resta:0.5 },
  { atta:"次元",attd:"王",resta:0.5 },
  { atta:"次元",attd:"混沌",resta:0.5 },
  { atta:"次元",attd:"神灵",resta:0.5 },
  { atta:"次元",attd:"轮回",resta:0.5 },

  { atta:"次元",attd:"暗影",resta:0 },
  
  { atta:"地面",attd:"火",resta:2 },
  { atta:"地面",attd:"电",resta:2 },
  { atta:"地面",attd:"机械",resta:2 },
  { atta:"地面",attd:"王",resta:2 },
  { atta:"地面",attd:"王",resta:2 },

  { atta:"地面",attd:"草",resta:0.5 },
  { atta:"地面",attd:"超能",resta:0.5 },
  { atta:"地面",attd:"暗影",resta:0.5 },
  { atta:"地面",attd:"龙",resta:0.5 },
  { atta:"地面",attd:"圣灵",resta:0.5 },
  { atta:"地面",attd:"自然",resta:0.5 },
  { atta:"地面",attd:"神灵",resta:0.5 },
  { atta:"地面",attd:"虫",resta:0.5 },

  { atta:"地面",attd:"飞行",resta:0 },
  
  { atta:"电",attd:"水",resta:2 },
  { atta:"电",attd:"飞行",resta:2 },
  { atta:"电",attd:"暗影",resta:2 },
  { atta:"电",attd:"次元",resta:2 },
  { atta:"电",attd:"混沌",resta:2 },
  { atta:"电",attd:"虚空",resta:2 },

  { atta:"电",attd:"草",resta:0.5 },
  { atta:"电",attd:"电",resta:0.5 },
  { atta:"电",attd:"神秘",resta:0.5 },
  { atta:"电",attd:"圣灵",resta:0.5 },
  { atta:"电",attd:"自然",resta:0.5 },
  { atta:"电",attd:"神灵",resta:0.5 },
  
  { atta:"电",attd:"地面",resta:0 },

  { atta:"飞行",attd:"草",resta:2 },
  { atta:"飞行",attd:"战斗",resta:2 },
  { atta:"飞行",attd:"虫",resta:2 },

  { atta:"飞行",attd:"电",resta:0.5 },
  { atta:"飞行",attd:"机械",resta:0.5 },
  { atta:"飞行",attd:"次元",resta:0.5 },
  { atta:"飞行",attd:"邪灵",resta:0.5 },
  { atta:"飞行",attd:"自然",resta:0.5 },
  { atta:"飞行",attd:"混沌",resta:0.5 },

  { atta:"光",attd:"超能",resta:2 },
  { atta:"光",attd:"暗影",resta:2 },
  { atta:"光",attd:"虫",resta:2 },

  { atta:"光",attd:"机械",resta:0.5 },
  { atta:"光",attd:"冰",resta:0.5 },
  { atta:"光",attd:"光",resta:0.5 },
  { atta:"光",attd:"圣灵",resta:0.5 },
  { atta:"光",attd:"邪灵",resta:0.5 },
  { atta:"光",attd:"自然",resta:0.5 },
  { atta:"光",attd:"神灵",resta:0.5 },
  { atta:"光",attd:"轮回",resta:0.5 },
  { atta:"光",attd:"虚空",resta:0.5 },

  { atta:"光",attd:"草",resta:0 },

  { atta:"混沌",attd:"飞行",resta:2 },
  { atta:"混沌",attd:"冰",resta:2 },
  { atta:"混沌",attd:"神秘",resta:2 },
  { atta:"混沌",attd:"次元",resta:2 },
  { atta:"混沌",attd:"邪灵",resta:2 },
  { atta:"混沌",attd:"自然",resta:2 },
  { atta:"混沌",attd:"神灵",resta:2 },

  { atta:"混沌",attd:"电",resta:0.5 },
  { atta:"混沌",attd:"机械",resta:0.5 },
  { atta:"混沌",attd:"战斗",resta:0.5 },
  { atta:"混沌",attd:"轮回",resta:0.5 },
  
  { atta:"混沌",attd:"虚空",resta:0 },

  { atta:"机械",attd:"冰",resta:2 },
  { atta:"机械",attd:"战斗",resta:2 },
  { atta:"机械",attd:"远古",resta:2 },
  { atta:"机械",attd:"邪灵",resta:2 },
  { atta:"机械",attd:"神灵",resta:2 },

  { atta:"机械",attd:"水",resta:0.5 },
  { atta:"机械",attd:"火",resta:0.5 },
  { atta:"机械",attd:"电",resta:0.5 },
  { atta:"机械",attd:"机械",resta:0.5 },
  { atta:"机械",attd:"次元",resta:0.5 },
  
  { atta:"龙",attd:"冰",resta:2 },
  { atta:"龙",attd:"龙",resta:2 },
  { atta:"龙",attd:"圣灵",resta:2 },
  { atta:"龙",attd:"邪灵",resta:2 },
  
  { atta:"龙",attd:"草",resta:0.5 },
  { atta:"龙",attd:"水",resta:0.5 },
  { atta:"龙",attd:"火",resta:0.5 },
  { atta:"龙",attd:"电",resta:0.5 },
  { atta:"龙",attd:"远古",resta:0.5 },
  { atta:"龙",attd:"虫",resta:0.5 },

  { atta:"轮回",attd:"光",resta:2 },
  { atta:"轮回",attd:"暗影",resta:2 },
  { atta:"轮回",attd:"圣灵",resta:2 },
  { atta:"轮回",attd:"次元",resta:2 },
  { atta:"轮回",attd:"邪灵",resta:2 },
  { atta:"轮回",attd:"混沌",resta:2 },
  
  { atta:"轮回",attd:"冰",resta:0.5 },
  { atta:"轮回",attd:"超能",resta:0.5 },
  { atta:"轮回",attd:"自然",resta:0.5 },
  { atta:"轮回",attd:"虚空",resta:0.5 },

  { atta:"神灵",attd:"草",resta:2 },
  { atta:"神灵",attd:"水",resta:2 },
  { atta:"神灵",attd:"火",resta:2 },
  { atta:"神灵",attd:"电",resta:2 },
  { atta:"神灵",attd:"冰",resta:2 },
  { atta:"神灵",attd:"远古",resta:2 },
  { atta:"神灵",attd:"邪灵",resta:2 },
  { atta:"神灵",attd:"混沌",resta:2 },
  
  { atta:"神灵",attd:"机械",resta:0.5 },
  { atta:"神灵",attd:"战斗",resta:0.5 },
  { atta:"神灵",attd:"龙",resta:0.5 },

  { atta:"神秘",attd:"电",resta:2 },
  { atta:"神秘",attd:"神秘",resta:2 },
  { atta:"神秘",attd:"圣灵",resta:2 },
  { atta:"神秘",attd:"自然",resta:2 },
  { atta:"神秘",attd:"王",resta:2 },
  { atta:"神秘",attd:"神灵",resta:2 },
  { atta:"神秘",attd:"轮回",resta:2 },

  { atta:"神秘",attd:"地面",resta:0.5 },
  { atta:"神秘",attd:"战斗",resta:0.5 },
  { atta:"神秘",attd:"邪灵",resta:0.5 },
  { atta:"神秘",attd:"混沌",resta:0.5 },
  { atta:"神秘",attd:"虫",resta:0.5 },

  { atta:"圣灵",attd:"草",resta:2 },
  { atta:"圣灵",attd:"水",resta:2 },
  { atta:"圣灵",attd:"火",resta:2 },
  { atta:"圣灵",attd:"电",resta:2 },
  { atta:"圣灵",attd:"冰",resta:2 },
  { atta:"圣灵",attd:"远古",resta:2 },
  { atta:"圣灵",attd:"混沌",resta:2 },
  
  { atta:"圣灵",attd:"机械",resta:0.5 },
  { atta:"圣灵",attd:"战斗",resta:0.5 },
  { atta:"圣灵",attd:"龙",resta:0.5 },
  { atta:"圣灵",attd:"轮回",resta:0.5 },

  { atta:"王",attd:"战斗",resta:2 },
  { atta:"王",attd:"暗影",resta:2 },
  { atta:"王",attd:"次元",resta:2 },
  { atta:"王",attd:"邪灵",resta:2 },

  { atta:"王",attd:"超能",resta:0.5 },
  { atta:"王",attd:"自然",resta:0.5 },
  { atta:"王",attd:"虫",resta:0.5 },

  { atta:"邪灵",attd:"光",resta:2 },
  { atta:"邪灵",attd:"暗影",resta:2 },
  { atta:"邪灵",attd:"神秘",resta:2 },
  { atta:"邪灵",attd:"次元",resta:2 },
  { atta:"邪灵",attd:"自然",resta:2 },
  
  { atta:"邪灵",attd:"机械",resta:0.5 },
  { atta:"邪灵",attd:"冰",resta:0.5 },
  { atta:"邪灵",attd:"超能",resta:0.5 },
  { atta:"邪灵",attd:"圣灵",resta:0.5 },
  { atta:"邪灵",attd:"王",resta:0.5 },
  { atta:"邪灵",attd:"混沌",resta:0.5 },
  { atta:"邪灵",attd:"轮回",resta:0.5 },

  { atta:"邪灵",attd:"神灵",resta:0 },

  { atta:"虚空",attd:"超能",resta:2 },
  { atta:"虚空",attd:"战斗",resta:2 },
  { atta:"虚空",attd:"光",resta:2 },
  { atta:"虚空",attd:"神秘",resta:2 },
  { atta:"虚空",attd:"自然",resta:2 },
  { atta:"虚空",attd:"轮回",resta:2 },

  { atta:"虚空",attd:"飞行",resta:0.5 },
  { atta:"虚空",attd:"暗影",resta:0.5 },
  { atta:"虚空",attd:"圣灵",resta:0.5 },
  { atta:"虚空",attd:"次元",resta:0.5 },

  { atta:"远古",attd:"草",resta:2 },
  { atta:"远古",attd:"飞行",resta:2 },
  { atta:"远古",attd:"神秘",resta:2 },
  { atta:"远古",attd:"龙",resta:2 },
  { atta:"远古",attd:"虚空",resta:2 },

  { atta:"远古",attd:"机械",resta:0.5 },
  { atta:"远古",attd:"冰",resta:0.5 },
  { atta:"远古",attd:"王",resta:0.5 },
  { atta:"远古",attd:"轮回",resta:0.5 },

  { atta:"战斗",attd:"机械",resta:2 },
  { atta:"战斗",attd:"冰",resta:2 },
  { atta:"战斗",attd:"龙",resta:2 },
  { atta:"战斗",attd:"圣灵",resta:2 },

  { atta:"战斗",attd:"超能",resta:0.5 },
  { atta:"战斗",attd:"战斗",resta:0.5 },
  { atta:"战斗",attd:"暗影",resta:0.5 },
  { atta:"战斗",attd:"邪灵",resta:0.5 },
  { atta:"战斗",attd:"王",resta:0.5 },

  
  { atta:"自然",attd:"草",resta:2 },
  { atta:"自然",attd:"水",resta:2 },
  { atta:"自然",attd:"火",resta:2 },
  { atta:"自然",attd:"飞行",resta:2 },
  { atta:"自然",attd:"电",resta:2 },
  { atta:"自然",attd:"地面",resta:2 },
  { atta:"自然",attd:"光",resta:2 },
  { atta:"自然",attd:"王",resta:2 },
  { atta:"自然",attd:"轮回",resta:2 },

  { atta:"自然",attd:"机械",resta:0.5 },
  { atta:"自然",attd:"超能",resta:0.5 },
  { atta:"自然",attd:"战斗",resta:0.5 },
  { atta:"自然",attd:"暗影",resta:0.5 },
  { atta:"自然",attd:"神秘",resta:0.5 },
  { atta:"自然",attd:"次元",resta:0.5 },
  { atta:"自然",attd:"邪灵",resta:0.5 },
  { atta:"自然",attd:"混沌",resta:0.5 },
  { atta:"自然",attd:"虚空",resta:0.5 },
]

/** 单属性 */
var single = [
  { attr1:"火",icon:"huo.png" },
  { attr1:"水",icon:"shui.png" },
  { attr1:"草",icon:"cao.png" },
  { attr1:"飞行",icon:"feixing.png" },
  { attr1:"电",icon:"dian.png" },
  { attr1:"地面",icon:"dimian.png" },
  { attr1:"机械",icon:"jixie.png" },
  { attr1:"冰",icon:"bing.png" },
  { attr1:"超能",icon:"chaoneng.png" },
  { attr1:"普通",icon:"putong.png" },
  { attr1:"战斗",icon:"zhandou.png" },
  { attr1:"暗影",icon:"anying.png" },
  { attr1:"光",  icon:"guang.png" },
  { attr1:"龙",  icon:"long.png" },
  { attr1:"神秘",icon:"shenmi.png" },
  { attr1:"圣灵",icon:"shengling.png" },
  { attr1:"次元",icon:"ciyuan.png" },
  { attr1:"远古",icon:"yuangu.png" },
  { attr1:"邪灵",icon:"xieling.png" },
  { attr1:"自然",icon:"ziran.png" },
  { attr1:"王",  icon:"wang.png" },
  { attr1:"混沌",icon:"hundun.png" },
  { attr1:"神灵",icon:"shenling.png" },
  { attr1:"轮回",icon:"lunhui.png" },
  { attr1:"虫",  icon:"chong.png" },
  { attr1:"虚空",icon:"xukong.png" },
]
/** 双属性 */
var double = [
  { attr1:"草",attr2:"超能",icon:"caochaoneng.png" },
  { attr1:"机械",attr2:"地面",icon:"jixiedimian.png" },
  { attr1:"飞行",attr2:"超能",icon:"feixingchaoneng.png" },
  { attr1:"水",attr2:"暗影",icon:"shuianying.png" },
  { attr1:"战斗",attr2:"火",icon:"zhandouhuo.png" },
  { attr1:"水",attr2:"龙",icon:"shuilong.png" },
  { attr1:"冰",attr2:"光",icon:"bingguang.png" },
  { attr1:"冰",attr2:"暗影",icon:"binganying.png" },
  { attr1:"战斗",attr2:"地面",icon:"zhandoudimian.png" },
  { attr1:"火",attr2:"超能",icon:"huochaoneng.png" },
  { attr1:"飞行",attr2:"光",icon:"feixingguang.png" },
  { attr1:"地面",attr2:"暗影",icon:"dimiananying.png" },
  { attr1:"冰",attr2:"超能",icon:"bingchaoneng.png" },
  { attr1:"电",attr2:"火",icon:"dianhuo.png" },
  { attr1:"水",attr2:"超能",icon:"shuichaoneng.png" },
  { attr1:"电",attr2:"冰",icon:"dianbing.png" },
  { attr1:"火",attr2:"龙",icon:"huolong.png" },
  { attr1:"草",attr2:"暗影",icon:"caoanying.png" },
  { attr1:"机械",attr2:"超能",icon:"" },
  { attr1:"战斗",attr2:"暗影",icon:"" },
  { attr1:"草",attr2:"战斗",icon:"" },
  { attr1:"飞行",attr2:"龙",icon:"" },
  { attr1:"地面",attr2:"龙",icon:"" },
  { attr1:"电",attr2:"战斗",icon:"" },
  { attr1:"火",attr2:"飞行",icon:"" },
  { attr1:"机械",attr2:"龙",icon:"" },
  { attr1:"冰",attr2:"龙",icon:"" },
  { attr1:"神秘",attr2:"暗影",icon:"" },
  { attr1:"光",attr2:"神秘",icon:"" },
  { attr1:"电",attr2:"暗影",icon:"" },
  { attr1:"飞行",attr2:"神秘",icon:"" },
  { attr1:"地面",attr2:"超能",icon:"" },  
  { attr1:"暗影",attr2:"龙",icon:"" },
  { attr1:"火",attr2:"神秘",icon:"" },
  { attr1:"远古",attr2:"战斗",icon:"" },
  { attr1:"光",attr2:"战斗",icon:"" },
  { attr1:"神秘",attr2:"战斗",icon:"" },
  { attr1:"次元",attr2:"战斗",icon:"" },
  { attr1:"暗影",attr2:"圣灵",icon:"" },
  { attr1:"邪灵",attr2:"神秘",icon:"" },
  { attr1:"远古",attr2:"龙",icon:"" },
  { attr1:"光",attr2:"次元",icon:"" },
  { attr1:"远古",attr2:"圣灵",icon:"" },
  { attr1:"电",attr2:"龙",icon:"" },
  { attr1:"光",attr2:"火",icon:"" },
  { attr1:"光",attr2:"暗影",icon:"" },
  { attr1:"邪灵",attr2:"龙",icon:"" },
  { attr1:"远古",attr2:"神秘",icon:"" },
  { attr1:"机械",attr2:"次元",icon:"" },
  { attr1:"龙",attr2:"战斗",icon:"" },
  { attr1:"自然",attr2:"战斗",icon:"" },
  { attr1:"电",attr2:"次元",icon:"" },
  { attr1:"远古",attr2:"火",icon:"" },
  { attr1:"圣灵",attr2:"战斗",icon:"" },
  { attr1:"圣灵",attr2:"次元",icon:"" },
  { attr1:"圣灵",attr2:"电",icon:"" },
  { attr1:"地面",attr2:"远古",icon:"" },
  { attr1:"远古",attr2:"草",icon:"" },
  { attr1:"自然",attr2:"龙",icon:"" },
  { attr1:"冰",attr2:"神秘",icon:"" },
  { attr1:"神秘",attr2:"超能",icon:"" },
  { attr1:"飞行",attr2:"暗影",icon:"" },
  { attr1:"冰",attr2:"火",icon:"" },
  { attr1:"冰",attr2:"飞行",icon:"" },
  { attr1:"圣灵",attr2:"自然",icon:"" },
  { attr1:"混沌",attr2:"圣灵",icon:"" },
  { attr1:"圣灵",attr2:"光",icon:"" },
  { attr1:"远古",attr2:"邪灵",icon:"" },
  { attr1:"自然",attr2:"冰",icon:"" },
  { attr1:"暗影",attr2:"混沌",icon:"" },
  { attr1:"混沌",attr2:"战斗",icon:"" },
  { attr1:"混沌",attr2:"超能",icon:"" },
  { attr1:"圣灵",attr2:"超能",icon:"" },
  { attr1:"混沌",attr2:"地面",icon:"" },
  { attr1:"邪灵",attr2:"暗影",icon:"" },
  { attr1:"混沌",attr2:"远古",icon:"" },
  { attr1:"混沌",attr2:"邪灵",icon:"" },
  { attr1:"圣灵",attr2:"地面",icon:"" },
  { attr1:"暗影",attr2:"火",icon:"" },
  { attr1:"机械",attr2:"战斗",icon:"" },
  { attr1:"飞行",attr2:"电",icon:"" },
  { attr1:"混沌",attr2:"飞行",icon:"" },
  { attr1:"混沌",attr2:"龙",icon:"" },
  { attr1:"混沌",attr2:"火",icon:"" },
  { attr1:"圣灵",attr2:"火",icon:"" },
  { attr1:"地面",attr2:"神秘",icon:"" },
  { attr1:"混沌",attr2:"次元",icon:"" },
  { attr1:"混沌",attr2:"冰",icon:"" },
  { attr1:"自然",attr2:"神秘",icon:"" },
  { attr1:"虚空",attr2:"混沌",icon:"" },
  { attr1:"圣灵",attr2:"轮回",icon:"" },
  { attr1:"水",attr2:"次元",icon:"" },
  { attr1:"圣灵",attr2:"神秘",icon:"" },
  { attr1:"虚空",attr2:"邪灵",icon:"" },
  { attr1:"机械",attr2:"神秘",icon:"" },
  { attr1:"水",attr2:"神秘",icon:"" }
]

/**
 * 攻击系数计算
 * @param {*} attr 
 * @param {*} type 
 * @returns 
 */
function calcSingle(attr,type){
  /**1.获取数组节点 */
  let result = [];
  let single1 = [];
  let double1 = new Set();
  for (let i = 0; i < restrain.length; i++) {
    if((type?restrain[i].atta:restrain[i].attd)==attr){
      single1.push(restrain[i])
    }
  }
  
  single1.forEach(element=>{
    result.push(element)
  })
  for (let i = 0; i < single1.length; i++) {
    for (let j = 0; j < double.length; j++) {
      if(double[j].attr1==(type?single1[i].attd:single1[i].atta) || double[j].attr2==(type?single1[i].attd:single1[i].atta)){
        // 有被克制关系的双属性
        double1.add(double[j])
      }
    }
  }
  // 计算双属性攻击
  double1.forEach(element=>{
    let resta1 = 1,resta2 = 1
    for (let i = 0; i < single1.length; i++) {
      if(element.attr1==(type?single1[i].attd:single1[i].atta)){
        // console.log(element.attr1+"==="+single1[i].attd+"==="+single1[i].resta);
        resta1 = single1[i].resta
      }
      if(element.attr2==(type?single1[i].attd:single1[i].atta)){
        resta2 = single1[i].resta
      }
    }
    // console.log(element.attr1+"==="+element.attr2+"==="+resta1+"==="+resta2);
    result.push(type
      ? {atta:attr,attd:element.attr1,attd2:element.attr2,resta1:resta1,resta2:resta2,resta:calcRestrain(resta1,resta2)}
      : {atta:element.attr1,atta2:element.attr2,attd:attr,resta1:resta1,resta2:resta2,resta:calcRestrain(resta1,resta2)}
    )
  })
  return result;
}

/** 双属性攻击系数计算 */
function calcDouble(attr1,attr2,type){
  /** 1.获取数组节点 */
  let result = [];
  let single1 = [];
  for (let i = 0; i < restrain.length; i++) {
    if((type?restrain[i].atta:restrain[i].attd)==attr1 || (type?restrain[i].atta:restrain[i].attd)==attr2){
      let a = restrain[i]
      a["mark"] = 0
      single1.push(a)
    }
  }

  /** 2.合并克制公式计算 */
  for (let i = 0; i < single1.length; i++) {
    for (let j = i+1; j < single1.length; j++) {
      if((type?single1[i].attd:single1[i].atta)==(type?single1[j].attd:single1[j].atta)){
        single1[j].mark = 1
        single1[i]["resta1"] = single1[j].resta
      }
    }
  }
  single1.forEach(element => {
    if (element.mark==0){
      // console.log(element)
      if(undefined===element.resta1){ element["resta1"] = 1 }
      // console.log(element.attd+"==="+element.resta+"==="+element.resta1+"==="+calcRestrain(element.resta,element.resta1));
      result.push(type
        ? {atta:attr1,atta2:attr2,attd:element.attd,resta1:element.resta,resta2:element.resta1,resta:calcRestrain(element.resta,element.resta1)}
        : {atta:element.atta,attd:attr1,attd2:attr2,resta1:element.resta,resta2:element.resta1,resta:calcRestrain(element.resta,element.resta1)}
      )
    }
  });
  return result
}

function calcDouble2(attr1,attr2,type){
  /** 1.获取数组节点 */
  let result = [];
  let single1 = [];
  for (let i = 0; i < restrain.length; i++) {
    if((type?restrain[i].atta:restrain[i].attd)==attr1 || (type?restrain[i].atta:restrain[i].attd)==attr2){
      single1.push(restrain[i])
    }
  }
  // 双属性攻击计算
  let double1 = new Set();
  for (let i = 0; i < single1.length; i++) {
    for (let j = 0; j < double.length; j++) {
      if(double[j].attr1==(type?single1[i].attd:single1[i].atta) || double[j].attr2==(type?single1[i].attd:single1[i].atta)){
        // 有被克制关系
        double1.add(double[j])
      }
    }
  }
  
  let double2 = calcDouble(attr1,attr2,type)
  double2.forEach(element => {
    result.push(element)
  });
  // console.log(double2)
  // 计算双属性攻击
  double1.forEach(element=>{
    let resta1 = 1,resta2 = 1
    for (let i = 0; i < double2.length; i++) {
      if(element.attr1==(type?double2[i].attd:double2[i].atta)){
        // console.log(element.attr1+"==="+double2[i].attd+"==="+double2[i].resta);
        resta1 = double2[i].resta
      }
      if(element.attr2==(type?double2[i].attd:double2[i].atta)){
        // console.log(element.attr2+"==="+double2[i].attd+"==="+double2[i].resta);
        resta2 = double2[i].resta
      }
    }
    result.push(type
      ? {atta:attr1,atta2:attr2,attd:element.attr1,attd2:element.attr2,resta1:resta1,resta2:resta2,resta:((resta1+resta2)/2)}
      : {atta:element.attr1,atta2:element.attr2,attd:attr1,attd2:attr2,resta1:resta1,resta2:resta2,resta:((resta1+resta2)/2)}
    )
  })
  return result;
}

function calcRestrain(restrain1,restrain2){
  if(restrain1==2&&restrain2==2){
    return restrain1+restrain2
  } else if (restrain1==0 || restrain2==0){
    return (restrain1+restrain2) / 4
  } else {
    return (restrain1+restrain2) / 2
  }
}

//排序
function sortByKey(array,key){
  return array.sort(function(a,b){
      var x=a[key];
      var y=b[key];
      return ((x>y)?-1:((x<y)?1:0))
  })
}

function singleAtt(atta){
  console.log("============== 单属性攻击 ==============");
  let singleAttack = calcSingle(atta,true)
  sortByKey(singleAttack,"resta")
  console.log(singleAttack)
  return singleAttack
}
function doubleAtt(attr1,attr2){
  console.log("============== 双属性攻击 ==============");
  let doubleAttack = calcDouble2(attr1,attr2,true)
  sortByKey(doubleAttack,"resta")
  console.log(doubleAttack)
  return doubleAttack
}

function singleBeAtt(attr){
  console.log("============== 被单属性攻击 ==============");
  let singleBeAttack = calcSingle(attr,false)
  sortByKey(singleBeAttack,"resta")
  console.log(singleBeAttack)
  return singleBeAttack
}

function doubleBeAtt(attr1,attr2){
  console.log("============== 被双属性攻击 ==============");
  let doubleBeAttack = calcDouble2(attr1,attr2,false)
  sortByKey(doubleBeAttack,"resta")
  console.log(doubleBeAttack)
  return doubleBeAttack
}

