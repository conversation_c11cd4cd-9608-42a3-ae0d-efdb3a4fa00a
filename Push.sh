#!/bin/bash

# 检查是否在 Git 仓库内
if [ ! -d .git ]; then
    echo "❌ 当前目录不是一个 Git 仓库！" >&2
    exit 1
fi

# 颜色支持
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PARENT_NAME=$(basename "$(dirname "$SCRIPT_DIR")")

# 获取当前分支名称
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
echo "当前目录$PARENT_NAME，正在从 origin 拉取分支: $BRANCH_NAME"

# 执行 pull 操作
if ! git pull; then
    echo "${RED}❌ Git pull 拉取失败，请检查以下内容：${NC}" >&2
    echo "  - 网络连接是否正常" >&2
    echo "  - 是否有冲突文件（请手动解决冲突）" >&2
    echo "  - 当前分支是否跟踪远程分支 origin/$BRANCH_NAME" >&2
    echo "${RED}❌ 拉取失败,按任意键结束${NC}" >&2
    read -n 1 -s -r
    exit 1
fi

echo "${GREEN}✅ Git pull 拉取成功${NC}"

echo "请输入提交原因（输入回车退出）："
read -r REASON

# 如果用户输入空字符串则退出
if [[ -z "$REASON" ]]; then
    echo "⚠️ 提交原因不能为空，操作已取消。"
    exit 1
fi

# 添加文件变化到暂存区
git add .

# 提交更改并添加注释
git commit -m "${REASON}"

# 将更改推送到远程仓库
if ! git push origin "$BRANCH_NAME"; then
    echo "${RED}❌ Git push 推送失败，请检查：${NC}" >&2
    echo "  - 是否有权限推送到远程分支" >&2
    echo "  - 网络连接是否正常" >&2
    echo "  - 是否需要先拉取更新（可能有冲突）" >&2
    echo "${RED}❌ 推送失败,按任意键结束${NC}" >&2
    read -n 1 -s -r
    exit 1
fi

read -n 1 -s -r -p "提交完成，按任意键结束"
echo