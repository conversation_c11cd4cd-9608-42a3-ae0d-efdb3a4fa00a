function clearAll() {
  document.getElementById('inputText').value = '';
  document.getElementById('result').innerHTML = '';
  document.getElementById('convertBtn').disabled = true;
}
window.clearAll = clearAll;

function selectAllResult() {
  let res = document.getElementById('result');
  if (!res) return;
  let range = document.createRange();
  range.selectNodeContents(res);
  let sel = window.getSelection();
  sel.removeAllRanges();
  sel.addRange(range);
}
window.selectAllResult = selectAllResult;

const INPUT_KEY = 'convert_input_v1';
document.getElementById('inputText').addEventListener('input', function() {
  localStorage.setItem(INPUT_KEY, this.value);
});

window.onload = function() {
  let saved = localStorage.getItem(INPUT_KEY);
  if (saved) {
    document.getElementById('inputText').value = saved;
    document.getElementById('convertBtn').disabled = !saved.trim();
  }
  document.getElementById("inputText").focus();
  document.getElementById("inputText").addEventListener("keydown", function(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
      convert();
    }
  });
  document.getElementById("inputText").addEventListener("input", function() {
    document.getElementById("convertBtn").disabled = !this.value.trim();
  });
  loadHistory();
  renderHistory();
  renderTypeSelector();
};

function copyText(e){
  var textArea = document.getElementById("textArea")
  textArea.innerText = e.getAttribute("data-result")
  const range = document.createRange();
  range.selectNode(textArea);
  const selection = window.getSelection();
  if (selection.rangeCount > 0) selection.removeAllRanges();
  selection.addRange(range);
  document.execCommand('copy');
  // 复制成功提示
  let old = document.getElementById('copyTip');
  if (old) old.remove();
  let tip = document.createElement('div');
  tip.id = 'copyTip';
  tip.textContent = '已复制!';
  tip.style.cssText = 'position:fixed;top:30%;left:50%;transform:translate(-50%,-50%);background:#333;color:#ffd700;padding:10px 24px;border-radius:8px;z-index:9999;font-size:1.2rem;opacity:0.95;';
  document.body.appendChild(tip);
  setTimeout(()=>{ tip.remove(); }, 1200);
}
function createDiv(title,value){
  let htmlStr = "<h4>_title</h4><p>_value</p>";
  if (value && title !== "统计") {
    htmlStr += "<input type='button' value='复制' data-result=\"" + value + "\" class='result-btn' onclick='copyText(this)'>";
  }
  let dome = document.createElement("div")
  dome.className = "result"
  dome.innerHTML = htmlStr.replace("_title",title).replace("_value",value).replace("_value",value)
  if (title=="统计") { dome.innerHTML = "<h4>_title</h4><p>_value</p>".replace("_title",title).replace("_value",value) }
  return dome
}