

function showModal(id) {
  console.log(navMenuItems)
  const item = navMenuItems?navMenuItems.find(i => i.id == id):undefined;
  navMenuModel.id = Number(id);
  // 使用三目运算符结合Object.keys方法来检查对象是否为空
  let flag = isEmpty(item);
  $('modalTitle').innerText = flag?'新增菜单项':'编辑菜单项';
  $("menuId").value = flag ? 0 : item.id;
  $("icon").value = flag ? 'assets/icon/zl.png' : item.icon;
  $("title").value = flag ? '' : item.title;
  $("remark").value = flag ? '' : item.remark;
  $("url").value = flag ? '' : item.url;
  $('modal-container').classList.add('active');
  $('modal-overlay').classList.add('active');
  // 添加模态框动画效果
  setTimeout(() => {
    document.querySelectorAll('.modal-group input, .modal-group textarea').forEach((el, index) => {
      setTimeout(() => {
        el.classList.add('animate-in');
      }, index * 50);
    });
  }, 100);
}


function closeModal() {
  $('modal-container').classList.remove('active');
  $('modal-overlay').classList.remove('active');
  // 重置表单动画
  document.querySelectorAll('.modal-group input, .modal-group textarea').forEach(el => {
    el.classList.remove('animate-in');
  });
}

function saveEdit() {
  navMenuModel.icon = $("icon").value.trim();
  navMenuModel.title = $("title").value.trim();
  navMenuModel.url = $("url").value.trim();
  navMenuModel.remark = $("remark").value.trim();
  try {
    // 添加保存按钮加载状态
    const saveBtn = document.querySelector('.save-btn');
    saveBtn.innerHTML = '<span class="loading-spinner"></span> 保存中...';
    saveBtn.disabled = true;
    let id = navMenuModel.id;
    // 模拟异步操作
    console.log(navMenuModel)
    setTimeout(() => {
      if (id == 0) {
        navMenuModel.id = navMenuItems?navMenuItems.length + 1:1;
          console.log('新增菜单')
        // 新增菜单项
        if(network){
          console.log('新增菜单')
          axios.post(navMenuItemsApi,navMenuModel.toJson()).then(function({data}){
            if (data.code == 200){
              editNavMenuItems(id);
            } else {
              showToast('菜单项添加失败，请检查网络！', 'error');
            }
          }).catch(error => {
            showToast('菜单项添加失败，请检查网络！', 'error');
          })
        } else {
          editNavMenuItems(id);
        }
      } else {
          console.log('修改菜单')
        // 更新菜单项
        if(network){
          console.log('修改菜单')
          axios.put(navMenuItemsApi,navMenuModel.toJson()).then(function({data}){
            if (data.code == 200){
              editNavMenuItems(id);
            } else {
              showToast('菜单项更新失败，请检查网络！', 'error');
            }
          }).catch(error => {
            showToast('菜单项更新失败，请检查网络！', 'error');
          })
        } else {
          editNavMenuItems(id);
        }
      }
      closeModal();
      // 恢复按钮状态
      saveBtn.innerHTML = '保存';
      saveBtn.disabled = false;
    }, 500); // 模拟网络延迟
  } catch (error) {
    showToast('保存失败，请重试', 'error');

    // 恢复按钮状态
    const saveBtn = document.querySelector('.save-btn');
    saveBtn.innerHTML = '保存';
    saveBtn.disabled = false;
  }
}

function editNavMenuItems(id){
  if (id==0) {
    navMenuItems.push(navMenuModel.toJson());
    showToast('菜单项添加成功！', 'success');
  } else {
    const index = navMenuItems.findIndex(i => i.id == id);
    if (index !== -1) {
      // 创建新对象并替换原对象
      navMenuItems[index] = {
        ...navMenuItems[index],
        icon: navMenuModel.icon,
        title: navMenuModel.title,
        remark: navMenuModel.remark,
        url: navMenuModel.url
      };
      showToast('菜单项更新成功！', 'success');
    } else {
      showToast('更新失败，非法ID', 'error');
    }
  }
  
  // 保存到本地存储
  localStorage.setItem('menuItems', JSON.stringify(navMenuItems));
  clickNavbar(1);
}


function deleteMenu(id) {
  // 查找要删除的菜单项
  const itemToDelete = navMenuItems.find(i => i.id == id);
  if (!itemToDelete) return;
  // 使用自定义确认对话框
  createConfirmDialog(
    '确认删除',
    `确定要删除"${itemToDelete.title}"菜单项吗？<br>此操作不可恢复。`,
    () => {
      console.log('点击')
      try {
        // 添加删除动画
        const menuCard = document.querySelector(`.menu-item[data-id="${id}"]`);
        console.log(menuCard);
        if (menuCard) {
          menuCard.classList.add('deleting');
        }
        // 不管成不成功都删除本地缓存
        const index = navMenuItems.findIndex(i => i.id == id);
        console.log(index)
        console.log(navMenuItems[index])
        if (index !== -1) {
          // 创建新对象并替换原对象
          navMenuItems[index] = {
            ...navMenuItems[index],
            deleted: 1
          };
          if(network){
            axios.delete(navMenuItemsApi + '/' + id).then(function({data}){
              if (data.code == 200){
                showToast('删除成功', 'success');
              } else {
                throw new Error("系统错误，请狠狠骂站长！！！");
              }
            }).catch(error => {
              showToast('删除失败，请检查网络！', 'error');
            })
          }
          localStorage.setItem('menuItems', JSON.stringify(navMenuItems));
          showToast('删除成功', 'success');
          clickNavbar(1);
        } else {
          showToast('删除失败，非法ID', 'error');
        }
      } catch (error) {
        showToast(error.message, 'error');
      }
    }
  );
}

// 创建确认对话框
function createConfirmDialog(title, message, onConfirm, onCancel) {
  // 移除可能存在的旧对话框
  const oldDialog = document.getElementById('confirm-dialog');
  if (oldDialog) oldDialog.remove();

  // 创建对话框元素
  const dialog = document.createElement('div');
  dialog.id = 'confirm-dialog';
  dialog.className = 'confirm-dialog';

  dialog.innerHTML = `
    <div class="confirm-dialog-content">
      <h3>${title}</h3>
      <p>${message}</p>
      <div class="confirm-dialog-buttons">
        <button class="confirm-btn">确认</button>
        <button class="cancel-btn">取消</button>
      </div>
    </div>
  `;

  // 添加到页面
  document.body.appendChild(dialog);

  // 添加动画类
  setTimeout(() => dialog.classList.add('active'), 10);

  // 绑定事件
  dialog.querySelector('.confirm-btn').addEventListener('click', () => {
    dialog.classList.remove('active');
    setTimeout(() => {
      dialog.remove();
      if (typeof onConfirm === 'function') onConfirm();
    }, 300);
  });

  dialog.querySelector('.cancel-btn').addEventListener('click', () => {
    dialog.classList.remove('active');
    setTimeout(() => {
      dialog.remove();
      if (typeof onCancel === 'function') onCancel();
    }, 300);
  });

  // 点击背景关闭
  dialog.addEventListener('click', (e) => {
    if (e.target === dialog) {
      dialog.classList.remove('active');
      setTimeout(() => {
        dialog.remove();
        if (typeof onCancel === 'function') onCancel();
      }, 300);
    }
  });
}



function clearDatas() {
  // 使用自定义确认对话框
  createConfirmDialog(
    '确认清除',
    `确定要清除本地缓存数据吗？清除后本地操作将被覆盖<br><span style='colorred'>（联网操作无效）此操作不可恢复。<span>`,
    () => {
      localStorage.removeItem('navMenuItems');
      localStorage.removeItem('navItems');
      localStorage.removeItem('searchs');
      location.reload();
    }
  );
}


















