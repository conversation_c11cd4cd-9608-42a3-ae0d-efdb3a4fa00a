


// 通过ParentId获取导航数组
function filterNavItemsById(id) {
    if (!navItems){return [];}
    const filtered = navItems.filter(item =>
        item.parent_id == id && item.deleted == 0
    ).sort((a, b) => b.sort - a.sort);  // 添加排序逻辑
    console.log(navItems)
    return filtered; // 返回过滤后的数组，或者根据需要返回其他值，如 true 或 false
}

// 通过导航ID获取导航信息
function filterNavItemsParent(navId) {
    if (!navItems){return {};}
    const filtered =  navItems.filter(item =>
        item.id == navId && item.deleted == 0
    )[0];
    return filtered; // 返回过滤后的数组，或者根据需要返回其他值，如 true 或 false
}

// 通过导航ID获取菜单项
function filterMenuItemsById(id) {
    if (!navMenuItems){return [];}
    const filtered =  navMenuItems.filter(item =>
        item.nav_id == id && item.deleted == 0
    ).sort((a, b) => b.sort - a.sort);  // 添加排序逻辑
    return filtered; // 返回过滤后的数组，或者根据需要返回其他值，如 true 或 false
}


const navbarElement = $('navbar');
// 1. 首先生成头部相关数据
function renderNavbar() {
    let navbarHtml = '';
    const navItemsf = filterNavItemsById(0)
    if (!isEmpty(navItemsf)) {
        navItemsf.forEach((navItem,index) => {
            navbarHtml += `
                <a target="_self" name="nav" data-id="${navItem.id}" 
                    class="nav-item" onclick="clickNavbar('${navItem.id}')">
                    ${navItem.name}
                </a>`;
        })
    } else {
        // 数据为空时展示数据
        navbarHtml += `
            <a target="_self" name="nav" data-id="10"
                class="nav-item" onclick="clickNavbar(10)">
                未整理
            </a>
        `;
    }
    // 渲染动画
    animationFrame(navbarElement,navbarHtml,'.nav-item');
    clickNavbar(isEmpty(navItemsf)?10:navItemsf[navItemsf.length-1].id);
}

function clickNavbar(id) {
    const navLinks = document.querySelectorAll('.nav-item');
    navLinks.forEach(navLink => {
        if (navLink.getAttribute('data-id') == id) {
            navLink.classList.add('active');
        } else {
            navLink.classList.remove('active');
        }
    });
    renderMenu(id);
}




const menuWarpElement = $('menuWarp');
const menuSidebarElement = $('menuSidebar');
const menuEmpty = menuWarpElement.innerHTML;
const menuSidebarEmpty = menuSidebarElement.innerHTML;

function filterNavItems(query) {
    // 过滤菜单项
    return menuItems.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query)
    );
}

function renderMenu(id) {
    menuSidebarElement.innerHTML = ''; // 清空之前的内容
    let sidebarHtml = '';
    let contentHtml = '';
    const navItemsf = filterNavItemsById(id);
    menuWarpElement.innerHTML = ''; // 清空之前的内容
    if (isEmpty(navItemsf)) {
        menuSidebarElement.innerHTML = menuSidebarEmpty;
        menuWarpElement.innerHTML = menuEmpty;
        return; // 退出函数，不执行后续的代码，避免空数据导致错误
    }
    
    console.log(navMenuItems)
    navItemsf.forEach((navItem,index) => {
        const menuItems = filterMenuItemsById(navItem.id)
        if (menuItems.length > 0) {
            // 1. 生成侧边栏
            sidebarHtml += `
                <div class="menu-sidebar-item">
                    <a  onclick="sideScroll(event,'${index}')"
                        class="menu-sidebar-item-a" 
                        href="#sidebar${navItem.id}">${navItem.name}
                    </a>
                </div>
            `
            // 2. 生成contentHead部分
            let headHtml = '';
            headHtml = `
                <div class="menu-content-header" id="sidebar${navItem.id}">
                    <h4>${navItem.name}
                        ${navItem.id==99?`
                            <a class="menu-content-edit" onclick="toggleEdit()">
                                <svg width="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                                <span>编辑</span>
                            </a>`:``
                        }
                    </h4>
                </div>
            ` 
            // 3. 生成contentBodyItem部分
            let itemHtml = '';
            menuItems.forEach(menuItem => {
                let tags = '';
                if (menuItem.tag) {
                    menuItem.tag.split(',').forEach(tag => {
                        tags += `<span>${tag}</span>`;
                    });
                }
                itemHtml += `
                    <div class="menu-item" data-id="${menuItem.id}">
                        <div class="menu-item-warper">
                            <div class="menu-item-header" onclick="to('${menuItem.url}')">
                                <div class="menu-item-icon">
                                    <img src="${menuItem.icon}" width="32" height="32" alt="图片"></img>
                                </div>
                                <div class="menu-item-content">
                                <h4>${menuItem.title}</h4>
                                <p>${menuItem.remark}</p>
                                </div>
                            </div>
                            <div class="menu-item-footer">${tags}</div>
                        </div>
                        ${navItem.id==99?`
                            <div class="menu-button">
                                <button onclick="showModal('${menuItem.id}')">
                                    <svg width="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                </button>
                                <button onclick="deleteMenu('${menuItem.id}')">
                                    <svg width="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                                </button>
                            </div>
                            `:``
                        }
                    </div>
                `
            });
            let menuContentBodyElement = document.createElement('div');
            menuContentBodyElement.classList.add('menu-content-body');
            menuContentBodyElement.innerHTML = itemHtml;
            
            let menuContentElement = document.createElement('div');
            menuContentElement.classList.add('menu-content');
            menuContentElement.innerHTML = headHtml + menuContentBodyElement.outerHTML;
            contentHtml += menuContentElement.outerHTML;
        }
    });
    // 渲染动画
    animationFrame(menuSidebarElement,sidebarHtml,'.menu-sidebar-item');
    // 渲染动画
    animationFrame(menuWarpElement,contentHtml,'.menu-content');
}


// 动画渲染
function animationFrame(_element,_html,_item){
    // 使用requestAnimationFrame优化渲染性能
    requestAnimationFrame(() => {
        _element.innerHTML = _html;
        // 添加进入动画
        setTimeout(() => {
          const cards = document.querySelectorAll(_item);
          cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
        
            setTimeout(() => {
              card.style.transition = 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
              card.style.opacity = '1';
              card.style.transform = 'translateY(0)';
            }, index * 50);
          });
        }, 0);
    });
}


// main.js 数据加载好后
// 1. 渲染导航栏，初始化页面
renderNavbar();


