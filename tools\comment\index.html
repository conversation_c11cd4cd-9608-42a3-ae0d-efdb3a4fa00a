<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <link rel="stylesheet" href="../convert/assets/convert.css">
  <title>留言板 - 多内容评价</title>
  <style>
    .comment-board { max-width: 600px; margin: 2rem auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #eee; padding: 2rem 1.5rem; }
    .comment-title { font-size: 1.5rem; color: #4f364a; margin-bottom: 1.2rem; }
    .comment-form { display: flex; flex-direction: column; gap: 10px; margin-bottom: 1.5rem; }
    .comment-form input, .comment-form textarea, .comment-form select { border: 2px solid #b596b5; border-radius: 8px; padding: 8px 12px; font-size: 1rem; }
    .comment-form textarea { resize: vertical; min-height: 60px; }
    .comment-form button { align-self: flex-end; background: #b596b5; color: #fff; border: none; border-radius: 8px; padding: 8px 24px; font-size: 1rem; cursor: pointer; transition: background 0.2s; }
    .comment-form button:hover { background: #4f364a; }
    .comment-list { margin: 0; padding: 0; list-style: none; }
    .comment-item { border-bottom: 1px dashed #eee; padding: 1rem 0 0.5rem 0; }
    .comment-header { display: flex; align-items: center; gap: 10px; }
    .comment-username { color: #4f8cff; font-weight: bold; }
    .comment-time { color: #aaa; font-size: 0.9em; }
    .comment-content { margin: 0.5em 0 0.5em 0; color: #333; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; }
    .comment-actions { display: flex; gap: 16px; align-items: center; font-size: 0.95em; }
    .like-btn { color: #b596b5; background: none; border: none; cursor: pointer; font-weight: bold; }
    .like-btn.liked { color: #f56c6c; }
    .reply-btn { color: #409eff; background: none; border: none; cursor: pointer; }
    .reply-form { margin-top: 0.5em; }
    .reply-list { margin-left: 2em; }
    .reply-list.level-2,
    .reply-list.level-3,
    .reply-list.level-4 { margin-left: 0; } /* 只第一次回复缩进 */
    .sensitive-tip { color: #f56c6c; font-size: 0.95em; margin-top: 0.2em; }
    .reply-info { color: #888; font-size: 0.95em; margin-bottom: 2px; }
    .comment-select { width: 100%; }
    .collapse-btn {
      background: none;
      border: none;
      color: #b596b5;
      cursor: pointer;
      font-size: 0.98em;
      margin: 0.5em 0;
      padding: 0;
    }
  </style>
</head>
<body style="background-color: rgb(251, 238, 230);">
  <div class="comment-board">
    <div class="comment-title">菜单评价留言板</div>
    <form class="comment-form" id="mainCommentForm" autocomplete="off">
      <select id="menuSelect" class="comment-select" required>
        <option value="">请选择要评价的内容</option>
        <option value="菜单A">菜单A</option>
        <option value="菜单B">菜单B</option>
        <option value="菜单C">菜单C</option>
        <!-- 可根据实际内容动态生成 -->
      </select>
      <textarea id="content" maxlength="300" placeholder="写下你对该内容的评价..." required></textarea>
      <div class="sensitive-tip" id="mainSensitiveTip" style="display:none;"></div>
      <button type="submit">留言</button>
    </form>
    <ul class="comment-list" id="commentList"></ul>
  </div>
  <!-- 昵称设置弹窗 -->
  <div id="nicknameModal" style="display:none;position:fixed;left:0;top:0;width:100vw;height:100vh;z-index:99;background:rgba(0,0,0,0.15);">
    <div style="background:#fff;padding:2em 2em 1.5em 2em;border-radius:12px;box-shadow:0 2px 8px #eee;max-width:320px;margin:10vh auto;">
      <div style="font-size:1.2em;margin-bottom:1em;">设置你的昵称</div>
      <input id="nicknameInput" maxlength="16" style="width:100%;border:2px solid #b596b5;border-radius:8px;padding:8px 12px;font-size:1em;">
      <div id="nicknameTip" style="color:#f56c6c;font-size:0.95em;display:none;margin-top:0.5em;"></div>
      <button id="nicknameBtn" style="margin-top:1.2em;background:#b596b5;color:#fff;border:none;border-radius:8px;padding:8px 24px;font-size:1em;cursor:pointer;">确定</button>
    </div>
  </div>
  <script src="assets/index.js"></script>
  <script>
    // ====== 配置区 ======
    const MENU_LIST = ['菜单A', '菜单B', '菜单C'];
    const SENSITIVE_WORDS = ['傻', 'sb', '垃圾', '妈的', 'fuck', '操', '死', '滚', '狗', '草', 'cnm', 'nmsl'];
    const MAX_COMMENTS = 5; // 每个菜单最多显示5条，超出可收起

    function hasSensitive(str) {
      return SENSITIVE_WORDS.some(word => str.toLowerCase().includes(word));
    }
    function filterSensitive(str) {
      let s = str;
      SENSITIVE_WORDS.forEach(word => {
        const reg = new RegExp(word, 'gi');
        s = s.replace(reg, '*'.repeat(word.length));
      });
      return s;
    }
    function getComments() {
      return JSON.parse(localStorage.getItem('menu_comments') || '[]');
    }
    function saveComments(list) {
      localStorage.setItem('menu_comments', JSON.stringify(list));
    }
    function getNickname() {
      return localStorage.getItem('menu_nickname') || '';
    }
    function setNickname(nick) {
      localStorage.setItem('menu_nickname', nick);
    }
    function formatTime(ts) {
      const d = new Date(ts);
      return d.getFullYear() + '-' + (d.getMonth()+1).toString().padStart(2,'0') + '-' + d.getDate().toString().padStart(2,'0') +
        ' ' + d.getHours().toString().padStart(2,'0') + ':' + d.getMinutes().toString().padStart(2,'0');
    }
    // ====== 昵称设置弹窗 ======
    function showNicknameModal() {
      document.getElementById('nicknameModal').style.display = '';
      document.getElementById('nicknameInput').value = '';
      document.getElementById('nicknameInput').focus();
    }
    function hideNicknameModal() {
      document.getElementById('nicknameModal').style.display = 'none';
    }
    document.getElementById('nicknameBtn').onclick = function() {
      const val = document.getElementById('nicknameInput').value.trim();
      if (!val) {
        document.getElementById('nicknameTip').textContent = '昵称不能为空';
        document.getElementById('nicknameTip').style.display = '';
        return;
      }
      document.getElementById('nicknameTip').style.display = 'none';
      setNickname(val);
      hideNicknameModal();
    };
    document.getElementById('nicknameInput').addEventListener('keydown', function(e){
      if(e.key==='Enter') document.getElementById('nicknameBtn').click();
    });
    // ====== 渲染菜单下拉 ======
    (function(){
      const sel = document.getElementById('menuSelect');
      sel.innerHTML = '<option value="">请选择要评价的内容</option>' + MENU_LIST.map(m=>`<option value="${m}">${m}</option>`).join('');
    })();

    // ====== 渲染留言 ======
    function renderComments() {
      const list = getComments();
      const ul = document.getElementById('commentList');
      ul.innerHTML = '';
      MENU_LIST.forEach(menu => {
        const menuComments = list.filter(c=>c.menu===menu && !c.parent);
        if(menuComments.length === 0) return;
        // 菜单标题
        const menuTitle = document.createElement('li');
        menuTitle.style.cssText = 'font-weight:bold;color:#b596b5;font-size:1.1em;padding:0.7em 0 0.3em 0;';
        menuTitle.textContent = menu;
        ul.appendChild(menuTitle);

        let showAll = false;
        let visibleComments = menuComments;
        if(menuComments.length > MAX_COMMENTS && !menuComments._showAll) {
          visibleComments = menuComments.slice(0, MAX_COMMENTS);
        }
        visibleComments.forEach(comment => {
          ul.appendChild(renderCommentItem(comment, list, 1));
        });

        // 收起/展开按钮
        if(menuComments.length > MAX_COMMENTS) {
          const btn = document.createElement('button');
          btn.className = 'collapse-btn';
          btn.textContent = menuComments._showAll ? '收起部分评论' : `查看更多评论（${menuComments.length - MAX_COMMENTS}）`;
          btn.onclick = function() {
            menuComments._showAll = !menuComments._showAll;
            renderComments();
          };
          ul.appendChild(btn);
        }
      });
    }

    // ====== 渲染单条留言（含回复，全部平铺） ======
    function renderCommentItem(comment, allList) {
      const li = document.createElement('li');
      li.className = 'comment-item';
      li.innerHTML = `
        <div class="comment-header">
          <span class="comment-username">${comment.username}</span>
          <span class="comment-time">${formatTime(comment.time)}</span>
        </div>
        <div class="comment-content">${comment.content}</div>
        <div class="comment-actions">
          <button class="like-btn${comment.liked ? ' liked' : ''}">👍 <span>${comment.likes||0}</span></button>
          <button class="reply-btn">回复</button>
        </div>
      `;
      // 点赞
      li.querySelector('.like-btn').onclick = function() {
        if (comment.liked) return;
        comment.likes = (comment.likes||0) + 1;
        comment.liked = true;
        saveComments(allList);
        renderComments();
      };
      // 回复
      li.querySelector('.reply-btn').onclick = function() {
        showReplyForm(li, comment, allList);
      };

      // 渲染所有回复（全部平铺，且支持收起/展开）
      const replies = allList.filter(c=>c.parent===comment.id);
      if (replies.length) {
        const replyUl = document.createElement('ul');
        replyUl.className = 'reply-list level-1';

        // 展开/收起逻辑
        const MAX_REPLY = 3;
        let showAll = replies._showAll || false;
        let visibleReplies = replies;
        if (replies.length > MAX_REPLY && !showAll) {
          visibleReplies = replies.slice(0, MAX_REPLY);
        }
        visibleReplies.forEach(reply => {
          replyUl.appendChild(renderReplyItem(reply, comment, allList));
        });

        if (replies.length > MAX_REPLY) {
          const btn = document.createElement('button');
          btn.className = 'collapse-btn';
          btn.textContent = showAll ? '收起回复' : `查看更多回复（${replies.length - MAX_REPLY}）`;
          btn.onclick = function() {
            replies._showAll = !showAll;
            renderComments();
          };
          replyUl.appendChild(btn);
        }

        li.appendChild(replyUl);
      }
      return li;
    }

    // ====== 渲染回复项（全部平铺） ======
    function renderReplyItem(reply, parentComment, allList) {
      const li = document.createElement('li');
      li.className = 'comment-item';
      li.innerHTML = `
        <div class="reply-info">${reply.username} 回复 ${parentComment.username}</div>
        <div class="comment-content">${reply.content}</div>
        <div class="comment-actions">
          <button class="like-btn${reply.liked ? ' liked' : ''}">👍 <span>${reply.likes||0}</span></button>
          <button class="reply-btn">回复</button>
        </div>
      `;
      // 点赞
      li.querySelector('.like-btn').onclick = function() {
        if (reply.liked) return;
        reply.likes = (reply.likes||0) + 1;
        reply.liked = true;
        saveComments(allList);
        renderComments();
      };
      // 回复
      li.querySelector('.reply-btn').onclick = function() {
        showReplyForm(li, reply, allList, parentComment.menu);
      };
      // 不再递归嵌套
      return li;
    }

    // ====== 显示回复表单 ======
    function showReplyForm(parentLi, parentComment, allList, menuOverride, level) {
      if (parentLi.querySelector('.reply-form')) return;
      const form = document.createElement('form');
      form.className = 'reply-form comment-form';
      form.innerHTML = `
        <div style="font-size:0.98em;color:#888;margin-bottom:2px;">回复 ${parentComment.username}</div>
        <textarea class="reply-content" maxlength="300" placeholder="写下你的回复..." required></textarea>
        <div class="sensitive-tip" style="display:none;"></div>
        <button type="submit">回复</button>
      `;
      form.onsubmit = function(e) {
        e.preventDefault();
        const username = getNickname();
        const content = form.querySelector('.reply-content').value.trim();
        const tip = form.querySelector('.sensitive-tip');
        if (!username || !content) return;
        if (hasSensitive(content)) {
          tip.textContent = '含有敏感词，请修改后再提交。';
          tip.style.display = '';
          return;
        }
        const list = getComments();
        list.push({
          id: Date.now() + Math.floor(Math.random()*1000),
          username,
          content: filterSensitive(content),
          time: Date.now(),
          parent: parentComment.id,
          menu: menuOverride || parentComment.menu,
          likes: 0
        });
        saveComments(list);
        renderComments();
      };
      parentLi.appendChild(form);
    }

    // ====== 主留言表单 ======
    document.getElementById('mainCommentForm').onsubmit = function(e) {
      e.preventDefault();
      const menu = document.getElementById('menuSelect').value;
      const username = getNickname();
      const content = document.getElementById('content').value.trim();
      const tip = document.getElementById('mainSensitiveTip');
      if (!menu || !username || !content) return;
      if (hasSensitive(content)) {
        tip.textContent = '含有敏感词，请修改后再提交。';
        tip.style.display = '';
        return;
      }
      tip.style.display = 'none';
      const list = getComments();
      list.push({
        id: Date.now() + Math.floor(Math.random()*1000),
        username,
        content: filterSensitive(content),
        time: Date.now(),
        parent: null,
        menu,
        likes: 0
      });
      saveComments(list);
      this.reset();
      renderComments();
    };

    // ====== 首次进入强制设置昵称 ======
    window.addEventListener('DOMContentLoaded', function() {
      if (!getNickname()) {
        showNicknameModal();
      }
      renderComments();
    });
  </script>
</body>
</html>