<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html;" charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <link rel="shortcut icon" href="../../door/assets/icon/logo.png" type="image/x-icon"/>
  <link rel="stylesheet" href="assets/css/index.css">
  <title>智能转换</title>
</head>
<body>
<div id="app" class="container">
  <div class="row">
    <h3>智能转换</h3>
    <div class="convert-box">
      <input type="button" value="转 换" class="convert-btn" id="convertBtn" onclick="convert()">
      <textarea class="convert-text" id="inputText" placeholder="输入转换文字"></textarea>
    </div>
  </div>
  <div class="row" id="result"></div>
  <div class="row">
    <div class="hint">
      <p>目前只能转换支持，数字转银行大写、千位分隔符、小写转大写、大写转小写、大小写互转、汉字转拼音、提取数字字母和汉字功能、和统计字数</p>
      <p>正则表达式验证居然会造成浏览器卡死，真是离了个全家谱<br><span style="color:red">/^([1-9][0-9]*)+(.[0-9]*)?$/.test("11111111111111111111111111111111bb")<span></p>
    </div>
    <div><textarea id="textArea" style="opacity: 0; height: 1rem;"></textarea></div>
  </div>
</div>
<script src="assets/rmb.js"></script>
<script src="assets/word.js"></script>
<script src="assets/pinyin.js"></script>
<script src="assets/history.js"></script>
<script src="assets/type.js"></script>
<script src="assets/core.js"></script>
<script src="assets/ui.js"></script>
</body>
</html>